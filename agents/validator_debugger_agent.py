"""
Validator/Debugger Agent for Blender 3D Model Generation AI Agent System

This module implements a validator and debugger agent that analyzes Blender execution
errors, diagnoses problems using LLM, and generates code fix suggestions to enable
inner loop error correction with CodeGenerationAgent and BlenderExecutor.

Author: Augment Agent
Date: 2025-07-18
"""

import os
import re
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

import openai
from openai import OpenAI

# Import related modules
try:
    from agents.knowledge_agent import KnowledgeAgent, RetrievalResult
    from blender_interface.blender_executor import BlenderOutput, BlenderExecutionStatus
except ImportError as e:
    logging.warning(f"Import warning: {e}")
    # Define minimal interfaces for testing
    class RetrievalResult:
        pass
    class BlenderOutput:
        pass
    class BlenderExecutionStatus:
        pass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """Enumeration of common Blender Python error types."""
    SYNTAX_ERROR = "syntax_error"
    NAME_ERROR = "name_error"
    ATTRIBUTE_ERROR = "attribute_error"
    TYPE_ERROR = "type_error"
    VALUE_ERROR = "value_error"
    IMPORT_ERROR = "import_error"
    BPY_API_ERROR = "bpy_api_error"
    MESH_ERROR = "mesh_error"
    MATERIAL_ERROR = "material_error"
    ADDON_ERROR = "addon_error"
    UNKNOWN_ERROR = "unknown_error"


class DiagnosisConfidence(Enum):
    """Confidence levels for error diagnosis."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class ErrorDiagnosis:
    """Result of error diagnosis."""
    error_type: ErrorType
    confidence: DiagnosisConfidence
    description: str
    root_cause: str
    affected_lines: List[int]
    suggested_fixes: List[str]
    knowledge_context: List[str]
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ValidationResult:
    """Result of validation and debugging process."""
    is_valid: bool
    diagnosis: Optional[ErrorDiagnosis]
    fixed_code: Optional[str]
    fix_applied: bool
    validation_time: float
    retry_count: int
    metadata: Optional[Dict[str, Any]] = None


class ValidatorDebuggerError(Exception):
    """Custom exception for validator/debugger errors."""
    pass


@dataclass
class ValidatorDebuggerConfig:
    """Configuration for validator/debugger agent."""
    openai_model: str = "gpt-4"
    max_retries: int = 3
    use_knowledge_agent: bool = True
    enable_auto_fix: bool = True
    confidence_threshold: float = 0.6
    max_fix_attempts: int = 5


class ValidatorDebuggerAgent:
    """
    Validator/Debugger Agent for analyzing Blender execution errors and generating fixes.
    
    This agent analyzes BlenderExecutor output, classifies errors, diagnoses root causes
    using LLM, and generates code fix suggestions for inner loop error correction.
    """
    
    def __init__(self, config: Optional[ValidatorDebuggerConfig] = None,
                 knowledge_agent: Optional[KnowledgeAgent] = None,
                 openai_api_key: Optional[str] = None):
        """
        Initialize the ValidatorDebuggerAgent.
        
        Args:
            config: Configuration for the agent
            knowledge_agent: Knowledge agent for API documentation lookup
            openai_api_key: OpenAI API key for LLM calls
        """
        self.config = config or ValidatorDebuggerConfig()
        self.knowledge_agent = knowledge_agent
        
        # Initialize OpenAI client
        api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.warning("OpenAI API key not provided. LLM-based diagnosis will be disabled.")
            self.openai_client = None
        else:
            self.openai_client = OpenAI(api_key=api_key)
        
        # Error pattern definitions for classification
        self.error_patterns = self._initialize_error_patterns()
        
        logger.info("ValidatorDebuggerAgent initialized successfully")
    
    def _initialize_error_patterns(self) -> Dict[ErrorType, List[str]]:
        """Initialize regex patterns for error classification."""
        return {
            ErrorType.SYNTAX_ERROR: [
                r"SyntaxError: (.+)",
                r"invalid syntax",
                r"unexpected EOF while parsing"
            ],
            ErrorType.NAME_ERROR: [
                r"NameError: name '(.+?)' is not defined",
                r"NameError: (.+)"
            ],
            ErrorType.ATTRIBUTE_ERROR: [
                r"AttributeError: '(.+?)' object has no attribute '(.+?)'",
                r"AttributeError: (.+)"
            ],
            ErrorType.TYPE_ERROR: [
                r"TypeError: (.+)",
                r"unsupported operand type",
                r"takes \d+ positional arguments but \d+ were given"
            ],
            ErrorType.VALUE_ERROR: [
                r"ValueError: (.+)",
                r"invalid literal for",
                r"could not convert"
            ],
            ErrorType.IMPORT_ERROR: [
                r"ImportError: (.+)",
                r"ModuleNotFoundError: No module named '(.+?)'",
                r"cannot import name"
            ],
            ErrorType.BPY_API_ERROR: [
                r"bpy\.(.+?): (.+)",
                r"RuntimeError: Operator bpy\.ops\.(.+?) poll\(\) failed",
                r"context is incorrect"
            ],
            ErrorType.MESH_ERROR: [
                r"mesh\.(.+?): (.+)",
                r"bmesh\.(.+?): (.+)",
                r"Invalid mesh data"
            ],
            ErrorType.MATERIAL_ERROR: [
                r"material\.(.+?): (.+)",
                r"shader\.(.+?): (.+)",
                r"Material not found"
            ],
            ErrorType.ADDON_ERROR: [
                r"addon utils modules: (.+)",
                r"addon '(.+?)' not found",
                r"Failed to load addon"
            ]
        }
    
    def validate_and_debug(self, blender_output: BlenderOutput, 
                          original_code: str) -> ValidationResult:
        """
        Main method to validate Blender execution output and debug errors.
        
        Args:
            blender_output: Output from BlenderExecutor
            original_code: Original Python code that was executed
            
        Returns:
            ValidationResult with diagnosis and potential fixes
        """
        start_time = datetime.now()
        
        try:
            # Check if execution was successful
            if (hasattr(blender_output, 'status') and
                (blender_output.status == BlenderExecutionStatus.SUCCESS or
                 blender_output.status == "success")):
                validation_time = (datetime.now() - start_time).total_seconds()
                return ValidationResult(
                    is_valid=True,
                    diagnosis=None,
                    fixed_code=None,
                    fix_applied=False,
                    validation_time=validation_time,
                    retry_count=0,
                    metadata={"status": "success", "no_errors": True}
                )
            
            # Analyze errors
            diagnosis = self._analyze_errors(blender_output, original_code)
            
            # Generate fixes if enabled
            fixed_code = None
            fix_applied = False
            if self.config.enable_auto_fix and diagnosis:
                fixed_code = self._generate_code_fix(original_code, diagnosis)
                fix_applied = fixed_code is not None
            
            validation_time = (datetime.now() - start_time).total_seconds()
            
            return ValidationResult(
                is_valid=False,
                diagnosis=diagnosis,
                fixed_code=fixed_code,
                fix_applied=fix_applied,
                validation_time=validation_time,
                retry_count=0,
                metadata={
                    "blender_status": blender_output.status.value if hasattr(blender_output.status, 'value') else str(blender_output.status),
                    "return_code": blender_output.return_code,
                    "execution_time": blender_output.execution_time
                }
            )
            
        except Exception as e:
            logger.error(f"Validation and debugging failed: {e}")
            validation_time = (datetime.now() - start_time).total_seconds()
            return ValidationResult(
                is_valid=False,
                diagnosis=None,
                fixed_code=None,
                fix_applied=False,
                validation_time=validation_time,
                retry_count=0,
                metadata={"error": str(e)}
            )
    
    def _analyze_errors(self, blender_output: BlenderOutput, 
                       original_code: str) -> Optional[ErrorDiagnosis]:
        """
        Analyze errors from Blender execution output.
        
        Args:
            blender_output: Output from BlenderExecutor
            original_code: Original Python code
            
        Returns:
            ErrorDiagnosis if errors found, None otherwise
        """
        try:
            # Extract error information
            error_text = blender_output.stderr or ""
            if not error_text.strip():
                return None
            
            # Classify error type
            error_type = self._classify_error(error_text)
            
            # Extract affected lines
            affected_lines = self._extract_line_numbers(error_text)
            
            # Get knowledge context
            knowledge_context = self._get_error_knowledge_context(error_type, error_text)
            
            # Generate diagnosis using LLM
            if self.openai_client:
                diagnosis_result = self._llm_diagnose_error(
                    error_text, original_code, error_type, knowledge_context
                )
                
                return ErrorDiagnosis(
                    error_type=error_type,
                    confidence=diagnosis_result.get("confidence", DiagnosisConfidence.MEDIUM),
                    description=diagnosis_result.get("description", "Error analysis completed"),
                    root_cause=diagnosis_result.get("root_cause", "Unknown cause"),
                    affected_lines=affected_lines,
                    suggested_fixes=diagnosis_result.get("suggested_fixes", []),
                    knowledge_context=[ctx[:100] + "..." for ctx in knowledge_context],
                    metadata=diagnosis_result.get("metadata", {})
                )
            else:
                # Fallback to pattern-based diagnosis
                return self._pattern_based_diagnosis(error_type, error_text, affected_lines, knowledge_context)
                
        except Exception as e:
            logger.error(f"Error analysis failed: {e}")
            return None

    def _classify_error(self, error_text: str) -> ErrorType:
        """
        Classify error type based on error text patterns.

        Args:
            error_text: Error text from Blender execution

        Returns:
            Classified ErrorType
        """
        error_text_lower = error_text.lower()

        for error_type, patterns in self.error_patterns.items():
            for pattern in patterns:
                if re.search(pattern, error_text, re.IGNORECASE):
                    logger.debug(f"Classified error as {error_type.value} using pattern: {pattern}")
                    return error_type

        logger.debug("Could not classify error, returning UNKNOWN_ERROR")
        return ErrorType.UNKNOWN_ERROR

    def _extract_line_numbers(self, error_text: str) -> List[int]:
        """
        Extract line numbers from error traceback.

        Args:
            error_text: Error text containing traceback

        Returns:
            List of line numbers where errors occurred
        """
        line_numbers = []

        # Pattern for Python traceback line numbers
        line_patterns = [
            r'File ".*?", line (\d+)',
            r'line (\d+), in',
            r'line (\d+)'
        ]

        for pattern in line_patterns:
            matches = re.findall(pattern, error_text)
            for match in matches:
                try:
                    line_num = int(match)
                    if line_num not in line_numbers:
                        line_numbers.append(line_num)
                except ValueError:
                    continue

        return sorted(line_numbers)

    def _get_error_knowledge_context(self, error_type: ErrorType,
                                   error_text: str) -> List[str]:
        """
        Get relevant knowledge context for error diagnosis.

        Args:
            error_type: Classified error type
            error_text: Error text

        Returns:
            List of relevant knowledge context strings
        """
        if not self.knowledge_agent:
            return []

        try:
            # Build query based on error type
            query_parts = ["blender python error"]

            if error_type == ErrorType.BPY_API_ERROR:
                query_parts.append("bpy api usage")
            elif error_type == ErrorType.MESH_ERROR:
                query_parts.append("mesh operations bmesh")
            elif error_type == ErrorType.MATERIAL_ERROR:
                query_parts.append("material shader nodes")
            elif error_type == ErrorType.ADDON_ERROR:
                query_parts.append("addon installation")
            else:
                query_parts.append(error_type.value.replace("_", " "))

            # Extract specific API calls from error text
            api_matches = re.findall(r'bpy\.(\w+(?:\.\w+)*)', error_text)
            if api_matches:
                query_parts.extend(api_matches[:2])  # Add first 2 API calls

            query = " ".join(query_parts)

            # Query knowledge base
            results = self.knowledge_agent.query_knowledge(query, top_k=3)
            return [result.chunk.content for result in results]

        except Exception as e:
            logger.warning(f"Failed to get knowledge context: {e}")
            return []

    def _llm_diagnose_error(self, error_text: str, original_code: str,
                           error_type: ErrorType, knowledge_context: List[str]) -> Dict[str, Any]:
        """
        Use LLM to diagnose error and generate fix suggestions.

        Args:
            error_text: Error text from Blender
            original_code: Original Python code
            error_type: Classified error type
            knowledge_context: Relevant knowledge context

        Returns:
            Dictionary with diagnosis results
        """
        try:
            # Prepare context for LLM
            context_parts = [
                f"ERROR TYPE: {error_type.value}",
                f"ERROR MESSAGE:\n{error_text}",
                f"ORIGINAL CODE:\n{original_code}"
            ]

            if knowledge_context:
                context_parts.append("RELEVANT BLENDER API KNOWLEDGE:")
                for i, ctx in enumerate(knowledge_context[:2]):
                    context_parts.append(f"{i+1}. {ctx[:300]}...")

            context = "\n\n".join(context_parts)

            # Create diagnosis prompt
            prompt = self._create_diagnosis_prompt(context)

            # Call LLM
            response = self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert Blender Python developer and debugger."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1500
            )

            # Parse LLM response
            response_text = response.choices[0].message.content.strip()
            return self._parse_llm_diagnosis(response_text)

        except Exception as e:
            logger.error(f"LLM diagnosis failed: {e}")
            return {
                "confidence": DiagnosisConfidence.LOW,
                "description": f"LLM diagnosis failed: {e}",
                "root_cause": "Unable to determine cause",
                "suggested_fixes": [],
                "metadata": {"llm_error": str(e)}
            }

    def _create_diagnosis_prompt(self, context: str) -> str:
        """Create prompt for LLM error diagnosis."""
        return f"""
Analyze the following Blender Python error and provide a detailed diagnosis:

{context}

Please provide your analysis in the following JSON format:
{{
    "confidence": "high|medium|low",
    "description": "Brief description of the error",
    "root_cause": "Detailed explanation of what caused the error",
    "suggested_fixes": [
        "Specific fix suggestion 1",
        "Specific fix suggestion 2"
    ],
    "metadata": {{
        "api_issues": ["list of problematic API calls"],
        "common_mistake": "yes|no",
        "fix_complexity": "simple|moderate|complex"
    }}
}}

Focus on:
1. Identifying the exact cause of the error
2. Providing actionable fix suggestions
3. Referencing correct Blender API usage when applicable
4. Considering common Blender Python pitfalls
"""

    def _parse_llm_diagnosis(self, response_text: str) -> Dict[str, Any]:
        """Parse LLM diagnosis response."""
        try:
            # Try to extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1

            if json_start >= 0 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                diagnosis_data = json.loads(json_text)

                # Convert confidence string to enum
                confidence_str = diagnosis_data.get("confidence", "medium").lower()
                if confidence_str == "high":
                    diagnosis_data["confidence"] = DiagnosisConfidence.HIGH
                elif confidence_str == "low":
                    diagnosis_data["confidence"] = DiagnosisConfidence.LOW
                else:
                    diagnosis_data["confidence"] = DiagnosisConfidence.MEDIUM

                return diagnosis_data
            else:
                # Fallback parsing
                return self._fallback_parse_diagnosis(response_text)

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse LLM JSON response: {e}")
            return self._fallback_parse_diagnosis(response_text)

    def _fallback_parse_diagnosis(self, response_text: str) -> Dict[str, Any]:
        """Fallback parsing when JSON parsing fails."""
        # Extract key information using regex
        description = "Error analysis completed"
        root_cause = "Unable to parse detailed cause"
        suggested_fixes = []

        # Try to extract description
        desc_match = re.search(r'description["\']?\s*:\s*["\']([^"\']+)["\']', response_text, re.IGNORECASE)
        if desc_match:
            description = desc_match.group(1)

        # Try to extract root cause
        cause_match = re.search(r'root[_\s]?cause["\']?\s*:\s*["\']([^"\']+)["\']', response_text, re.IGNORECASE)
        if cause_match:
            root_cause = cause_match.group(1)

        # Try to extract fixes
        fix_matches = re.findall(r'["\']([^"\']*fix[^"\']*)["\']', response_text, re.IGNORECASE)
        suggested_fixes = fix_matches[:3]  # Take first 3 matches

        return {
            "confidence": DiagnosisConfidence.MEDIUM,
            "description": description,
            "root_cause": root_cause,
            "suggested_fixes": suggested_fixes,
            "metadata": {"parsing": "fallback"}
        }

    def _pattern_based_diagnosis(self, error_type: ErrorType, error_text: str,
                                affected_lines: List[int], knowledge_context: List[str]) -> ErrorDiagnosis:
        """
        Fallback pattern-based diagnosis when LLM is not available.

        Args:
            error_type: Classified error type
            error_text: Error text
            affected_lines: Line numbers with errors
            knowledge_context: Knowledge context

        Returns:
            ErrorDiagnosis based on patterns
        """
        # Define pattern-based diagnoses
        diagnoses = {
            ErrorType.SYNTAX_ERROR: {
                "description": "Python syntax error detected",
                "root_cause": "Invalid Python syntax in the generated code",
                "fixes": ["Check for missing colons, parentheses, or indentation", "Validate Python syntax"]
            },
            ErrorType.NAME_ERROR: {
                "description": "Undefined variable or function name",
                "root_cause": "Reference to undefined variable or function",
                "fixes": ["Define the missing variable", "Check for typos in variable names", "Import required modules"]
            },
            ErrorType.ATTRIBUTE_ERROR: {
                "description": "Object attribute access error",
                "root_cause": "Attempting to access non-existent attribute",
                "fixes": ["Check object type before accessing attributes", "Verify attribute names", "Ensure object is properly initialized"]
            },
            ErrorType.BPY_API_ERROR: {
                "description": "Blender Python API usage error",
                "root_cause": "Incorrect usage of Blender Python API",
                "fixes": ["Check Blender API documentation", "Verify context requirements", "Ensure objects exist before operations"]
            },
            ErrorType.TYPE_ERROR: {
                "description": "Type mismatch error",
                "root_cause": "Incorrect data type used in operation",
                "fixes": ["Check parameter types", "Convert data types as needed", "Verify function signatures"]
            }
        }

        diagnosis_info = diagnoses.get(error_type, {
            "description": f"Error of type {error_type.value}",
            "root_cause": "Error cause needs manual investigation",
            "fixes": ["Review error message and code", "Check Blender documentation"]
        })

        return ErrorDiagnosis(
            error_type=error_type,
            confidence=DiagnosisConfidence.MEDIUM,
            description=diagnosis_info["description"],
            root_cause=diagnosis_info["root_cause"],
            affected_lines=affected_lines,
            suggested_fixes=diagnosis_info["fixes"],
            knowledge_context=[ctx[:100] + "..." for ctx in knowledge_context],
            metadata={"method": "pattern_based"}
        )

    def _generate_code_fix(self, original_code: str, diagnosis: ErrorDiagnosis) -> Optional[str]:
        """
        Generate fixed code based on diagnosis.

        Args:
            original_code: Original code with errors
            diagnosis: Error diagnosis with fix suggestions

        Returns:
            Fixed code string or None if fix generation failed
        """
        if not self.openai_client:
            logger.warning("OpenAI client not available for code fix generation")
            return None

        try:
            # Prepare context for code fix
            context = self._prepare_fix_context(original_code, diagnosis)

            # Create fix prompt
            prompt = self._create_fix_prompt(context)

            # Call LLM for code fix
            response = self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert Blender Python developer. Fix the provided code based on the error diagnosis."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )

            fixed_code = response.choices[0].message.content.strip()

            # Extract code from markdown if present
            if "```python" in fixed_code:
                start = fixed_code.find("```python") + 9
                end = fixed_code.find("```", start)
                if end != -1:
                    fixed_code = fixed_code[start:end].strip()

            # Validate fixed code syntax
            if self._validate_code_syntax(fixed_code):
                logger.info("Successfully generated fixed code")
                return fixed_code
            else:
                logger.warning("Generated fixed code has syntax errors")
                return None

        except Exception as e:
            logger.error(f"Code fix generation failed: {e}")
            return None

    def _prepare_fix_context(self, original_code: str, diagnosis: ErrorDiagnosis) -> str:
        """Prepare context for code fix generation."""
        context_parts = [
            f"ERROR TYPE: {diagnosis.error_type.value}",
            f"ERROR DESCRIPTION: {diagnosis.description}",
            f"ROOT CAUSE: {diagnosis.root_cause}",
            f"AFFECTED LINES: {diagnosis.affected_lines}",
            f"SUGGESTED FIXES: {', '.join(diagnosis.suggested_fixes)}",
            f"ORIGINAL CODE:\n{original_code}"
        ]

        if diagnosis.knowledge_context:
            context_parts.append("RELEVANT API KNOWLEDGE:")
            for i, ctx in enumerate(diagnosis.knowledge_context[:2]):
                context_parts.append(f"{i+1}. {ctx}")

        return "\n\n".join(context_parts)

    def _create_fix_prompt(self, context: str) -> str:
        """Create prompt for code fix generation."""
        return f"""
Based on the error diagnosis below, please fix the Blender Python code:

{context}

Requirements:
1. Fix the specific error identified in the diagnosis
2. Maintain the original code's functionality and structure
3. Use correct Blender Python API syntax
4. Add error handling where appropriate
5. Include brief comments explaining the fixes

Please provide only the corrected Python code without additional explanations.
"""

    def _validate_code_syntax(self, code: str) -> bool:
        """
        Validate Python code syntax.

        Args:
            code: Python code to validate

        Returns:
            True if syntax is valid, False otherwise
        """
        try:
            compile(code, '<string>', 'exec')
            return True
        except SyntaxError as e:
            logger.debug(f"Code syntax validation failed: {e}")
            return False

    def create_inner_loop_fix_cycle(self, code_generation_agent, blender_executor,
                                   original_spec: Dict[str, Any], max_attempts: int = 3) -> Dict[str, Any]:
        """
        Create an inner loop fix cycle with CodeGenerationAgent and BlenderExecutor.

        Args:
            code_generation_agent: CodeGenerationAgent instance
            blender_executor: BlenderExecutor instance
            original_spec: Original model specification
            max_attempts: Maximum number of fix attempts

        Returns:
            Dictionary with cycle results
        """
        cycle_results = {
            "success": False,
            "attempts": 0,
            "final_code": None,
            "final_output": None,
            "error_history": [],
            "fix_history": []
        }

        try:
            current_code = None

            for attempt in range(max_attempts):
                cycle_results["attempts"] = attempt + 1
                logger.info(f"Inner loop attempt {attempt + 1}/{max_attempts}")

                # Generate or regenerate code
                if attempt == 0:
                    # Initial code generation
                    generation_result = code_generation_agent.generate_blender_code(original_spec)
                    current_code = generation_result.generated_code
                else:
                    # Regenerate with fix suggestions
                    last_diagnosis = cycle_results["error_history"][-1]
                    fix_context = {
                        "previous_error": last_diagnosis,
                        "fix_suggestions": last_diagnosis.suggested_fixes
                    }
                    generation_result = code_generation_agent.generate_blender_code(
                        original_spec, fix_context=fix_context
                    )
                    current_code = generation_result.generated_code

                # Execute code
                blender_output = blender_executor.execute_script(current_code)

                # Validate and debug
                validation_result = self.validate_and_debug(blender_output, current_code)

                if validation_result.is_valid:
                    # Success!
                    cycle_results["success"] = True
                    cycle_results["final_code"] = current_code
                    cycle_results["final_output"] = blender_output
                    logger.info(f"Inner loop succeeded after {attempt + 1} attempts")
                    break
                else:
                    # Record error and continue
                    if validation_result.diagnosis:
                        cycle_results["error_history"].append(validation_result.diagnosis)
                        if validation_result.fixed_code:
                            cycle_results["fix_history"].append(validation_result.fixed_code)
                            current_code = validation_result.fixed_code

                        logger.info(f"Attempt {attempt + 1} failed: {validation_result.diagnosis.description}")
                    else:
                        logger.info(f"Attempt {attempt + 1} failed: No diagnosis available")

            return cycle_results

        except Exception as e:
            logger.error(f"Inner loop cycle failed: {e}")
            cycle_results["error"] = str(e)
            return cycle_results
