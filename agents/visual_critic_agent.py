"""
Visual Critic Agent for Blender 3D Model Generation AI Agent System

This module implements a visual critic agent that compares rendered images with
original input images using multi-modal LLM, evaluates visual consistency across
multiple dimensions (shape, color, material, position), and generates high-level
design correction suggestions to form an outer feedback loop with SpecGenerationAgent.

Author: Augment Agent
Date: 2025-07-18
"""

import os
import json
import base64
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

import openai
from openai import OpenAI
from PIL import Image

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VisualDimension(Enum):
    """Visual evaluation dimensions."""
    SHAPE = "shape"
    COLOR = "color"
    MATERIAL = "material"
    POSITION = "position"
    SCALE = "scale"
    LIGHTING = "lighting"
    COMPOSITION = "composition"


class ConsistencyLevel(Enum):
    """Visual consistency levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    VERY_POOR = "very_poor"


class FeedbackPriority(Enum):
    """Feedback suggestion priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class VisualEvaluation:
    """Visual evaluation result for a specific dimension."""
    dimension: VisualDimension
    consistency_level: ConsistencyLevel
    score: float  # 0.0 to 1.0
    description: str
    specific_issues: List[str]
    confidence: float  # 0.0 to 1.0


@dataclass
class FeedbackSuggestion:
    """Design correction suggestion."""
    priority: FeedbackPriority
    dimension: VisualDimension
    issue_description: str
    suggested_correction: str
    actionable_steps: List[str]
    specificity_score: float  # 0.0 to 5.0
    operability_score: float  # 0.0 to 5.0


@dataclass
class VisualCriticResult:
    """Complete visual criticism result."""
    overall_consistency_score: float  # 0.0 to 1.0
    overall_consistency_level: ConsistencyLevel
    dimension_evaluations: List[VisualEvaluation]
    feedback_suggestions: List[FeedbackSuggestion]
    processing_time: float
    comparison_confidence: float
    metadata: Dict[str, Any]


@dataclass
class VisualCriticConfig:
    """Configuration for Visual Critic Agent."""
    openai_api_key: Optional[str] = None
    openai_model: str = "gpt-4o"  # GPT-4 with vision
    max_retries: int = 3
    retry_delay: float = 1.0
    evaluation_dimensions: List[VisualDimension] = None
    enable_detailed_analysis: bool = True
    min_confidence_threshold: float = 0.5
    
    def __post_init__(self):
        if self.evaluation_dimensions is None:
            self.evaluation_dimensions = [
                VisualDimension.SHAPE,
                VisualDimension.COLOR,
                VisualDimension.POSITION,
                VisualDimension.SCALE
            ]


class VisualCriticError(Exception):
    """Custom exception for visual critic errors."""
    pass


class VisualCriticAgent:
    """
    Visual Critic Agent for 3D model generation feedback.
    
    Compares rendered images with original input images using multi-modal LLM,
    evaluates visual consistency across multiple dimensions, and generates
    actionable design correction suggestions for outer loop feedback.
    """
    
    def __init__(self, config: Optional[VisualCriticConfig] = None):
        """
        Initialize Visual Critic Agent.
        
        Args:
            config: Configuration for the agent
        """
        self.config = config or VisualCriticConfig()
        
        # Initialize OpenAI client
        api_key = self.config.openai_api_key or os.getenv("OPENAI_API_KEY")
        if api_key:
            self.openai_client = OpenAI(api_key=api_key)
        else:
            logger.warning("No OpenAI API key provided. Visual criticism will be limited.")
            self.openai_client = None
    
    def evaluate_visual_consistency(self,
                                   original_image_path: Union[str, Path],
                                   rendered_image_path: Union[str, Path],
                                   context: Optional[Dict[str, Any]] = None) -> VisualCriticResult:
        """
        Evaluate visual consistency between original and rendered images.
        
        Args:
            original_image_path: Path to original input image
            rendered_image_path: Path to rendered Blender output image
            context: Optional context information (model spec, generation params, etc.)
            
        Returns:
            VisualCriticResult: Complete evaluation and feedback
            
        Raises:
            VisualCriticError: If evaluation fails
        """
        start_time = datetime.now()
        
        try:
            # Validate inputs
            original_path = Path(original_image_path)
            rendered_path = Path(rendered_image_path)
            
            if not original_path.exists():
                raise VisualCriticError(f"Original image not found: {original_path}")
            if not rendered_path.exists():
                raise VisualCriticError(f"Rendered image not found: {rendered_path}")
            
            # Perform visual evaluation
            if self.openai_client:
                result = self._evaluate_with_multimodal_llm(
                    original_path, rendered_path, context
                )
            else:
                result = self._evaluate_with_fallback_method(
                    original_path, rendered_path, context
                )
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            result.processing_time = processing_time
            
            logger.info(f"Visual evaluation completed in {processing_time:.2f}s")
            logger.info(f"Overall consistency: {result.overall_consistency_level.value} "
                       f"(score: {result.overall_consistency_score:.2f})")
            
            return result
            
        except Exception as e:
            logger.error(f"Visual evaluation failed: {e}")
            raise VisualCriticError(f"Failed to evaluate visual consistency: {str(e)}")
    
    def _evaluate_with_multimodal_llm(self,
                                     original_path: Path,
                                     rendered_path: Path,
                                     context: Optional[Dict[str, Any]]) -> VisualCriticResult:
        """Evaluate using multi-modal LLM."""
        try:
            # Encode images to base64
            original_b64 = self._encode_image_to_base64(original_path)
            rendered_b64 = self._encode_image_to_base64(rendered_path)
            
            # Create evaluation prompt
            prompt = self._create_evaluation_prompt(context)
            
            # Prepare messages for multi-modal LLM
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{original_b64}"}
                        },
                        {
                            "type": "text", 
                            "text": "RENDERED IMAGE (Blender output):"
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{rendered_b64}"}
                        }
                    ]
                }
            ]
            
            # Call multi-modal LLM with retry
            response = self._call_multimodal_llm_with_retry(messages)
            
            # Parse and structure response
            return self._parse_evaluation_response(response, str(original_path), str(rendered_path))
            
        except Exception as e:
            raise VisualCriticError(f"Multi-modal LLM evaluation failed: {str(e)}")
    
    def _encode_image_to_base64(self, image_path: Path) -> str:
        """Encode image to base64 string."""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise VisualCriticError(f"Failed to encode image {image_path}: {str(e)}")
    
    def _create_evaluation_prompt(self, context: Optional[Dict[str, Any]]) -> str:
        """Create evaluation prompt for multi-modal LLM."""
        dimensions_text = ", ".join([d.value for d in self.config.evaluation_dimensions])
        
        context_text = ""
        if context:
            context_text = f"\nContext Information:\n{json.dumps(context, indent=2)}\n"
        
        prompt = f"""
You are an expert 3D modeling and visual design critic. Compare the ORIGINAL IMAGE with the RENDERED IMAGE (Blender output) and evaluate visual consistency across these dimensions: {dimensions_text}.

{context_text}

Please provide a detailed evaluation in the following JSON format:

{{
    "overall_consistency_score": <float 0.0-1.0>,
    "overall_consistency_level": "<excellent|good|fair|poor|very_poor>",
    "comparison_confidence": <float 0.0-1.0>,
    "dimension_evaluations": [
        {{
            "dimension": "<dimension_name>",
            "consistency_level": "<excellent|good|fair|poor|very_poor>",
            "score": <float 0.0-1.0>,
            "description": "<detailed description>",
            "specific_issues": ["<issue1>", "<issue2>"],
            "confidence": <float 0.0-1.0>
        }}
    ],
    "feedback_suggestions": [
        {{
            "priority": "<critical|high|medium|low>",
            "dimension": "<dimension_name>",
            "issue_description": "<what's wrong>",
            "suggested_correction": "<how to fix it>",
            "actionable_steps": ["<step1>", "<step2>"],
            "specificity_score": <float 0.0-5.0>,
            "operability_score": <float 0.0-5.0>
        }}
    ]
}}

Focus on actionable, specific feedback that can guide model specification improvements.
"""
        return prompt.strip()

    def _call_multimodal_llm_with_retry(self, messages: List[Dict]) -> str:
        """Call multi-modal LLM with retry mechanism."""
        import time

        for attempt in range(self.config.max_retries):
            try:
                response = self.openai_client.chat.completions.create(
                    model=self.config.openai_model,
                    messages=messages,
                    temperature=0.1,
                    max_tokens=2000
                )
                return response.choices[0].message.content

            except Exception as e:
                logger.warning(f"LLM call attempt {attempt + 1} failed: {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay)
                else:
                    raise VisualCriticError(f"All LLM call attempts failed: {str(e)}")

    def _parse_evaluation_response(self, response: str,
                                  original_path: str,
                                  rendered_path: str) -> VisualCriticResult:
        """Parse LLM evaluation response into structured result."""
        try:
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1

            if json_start >= 0 and json_end > json_start:
                json_text = response[json_start:json_end]
                data = json.loads(json_text)
            else:
                raise ValueError("No valid JSON found in response")

            # Parse dimension evaluations
            dimension_evaluations = []
            for eval_data in data.get("dimension_evaluations", []):
                evaluation = VisualEvaluation(
                    dimension=VisualDimension(eval_data["dimension"]),
                    consistency_level=ConsistencyLevel(eval_data["consistency_level"]),
                    score=float(eval_data["score"]),
                    description=eval_data["description"],
                    specific_issues=eval_data.get("specific_issues", []),
                    confidence=float(eval_data.get("confidence", 0.8))
                )
                dimension_evaluations.append(evaluation)

            # Parse feedback suggestions
            feedback_suggestions = []
            for feedback_data in data.get("feedback_suggestions", []):
                suggestion = FeedbackSuggestion(
                    priority=FeedbackPriority(feedback_data["priority"]),
                    dimension=VisualDimension(feedback_data["dimension"]),
                    issue_description=feedback_data["issue_description"],
                    suggested_correction=feedback_data["suggested_correction"],
                    actionable_steps=feedback_data.get("actionable_steps", []),
                    specificity_score=float(feedback_data.get("specificity_score", 3.0)),
                    operability_score=float(feedback_data.get("operability_score", 3.0))
                )
                feedback_suggestions.append(suggestion)

            # Create result
            return VisualCriticResult(
                overall_consistency_score=float(data["overall_consistency_score"]),
                overall_consistency_level=ConsistencyLevel(data["overall_consistency_level"]),
                dimension_evaluations=dimension_evaluations,
                feedback_suggestions=feedback_suggestions,
                processing_time=0.0,  # Will be set by caller
                comparison_confidence=float(data.get("comparison_confidence", 0.8)),
                metadata={
                    "original_image": original_path,
                    "rendered_image": rendered_path,
                    "evaluation_method": "multimodal_llm",
                    "model_used": self.config.openai_model
                }
            )

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.warning(f"Failed to parse LLM response, using fallback: {e}")
            return self._create_fallback_result(original_path, rendered_path)

    def _evaluate_with_fallback_method(self,
                                      original_path: Path,
                                      rendered_path: Path,
                                      context: Optional[Dict[str, Any]]) -> VisualCriticResult:
        """Fallback evaluation method when LLM is not available."""
        logger.info("Using fallback evaluation method (basic image comparison)")

        try:
            # Basic image comparison using PIL
            original_img = Image.open(original_path)
            rendered_img = Image.open(rendered_path)

            # Simple similarity metrics
            size_similarity = self._calculate_size_similarity(original_img, rendered_img)
            color_similarity = self._calculate_basic_color_similarity(original_img, rendered_img)

            # Create basic evaluations
            dimension_evaluations = [
                VisualEvaluation(
                    dimension=VisualDimension.SHAPE,
                    consistency_level=ConsistencyLevel.FAIR,
                    score=size_similarity,
                    description=f"Basic size comparison (similarity: {size_similarity:.2f})",
                    specific_issues=["Limited analysis without LLM"],
                    confidence=0.5
                ),
                VisualEvaluation(
                    dimension=VisualDimension.COLOR,
                    consistency_level=ConsistencyLevel.FAIR,
                    score=color_similarity,
                    description=f"Basic color comparison (similarity: {color_similarity:.2f})",
                    specific_issues=["Limited analysis without LLM"],
                    confidence=0.5
                )
            ]

            # Create basic feedback
            feedback_suggestions = [
                FeedbackSuggestion(
                    priority=FeedbackPriority.MEDIUM,
                    dimension=VisualDimension.SHAPE,
                    issue_description="Limited evaluation without multi-modal LLM",
                    suggested_correction="Configure OpenAI API for detailed analysis",
                    actionable_steps=["Set OPENAI_API_KEY environment variable"],
                    specificity_score=2.0,
                    operability_score=4.0
                )
            ]

            overall_score = (size_similarity + color_similarity) / 2
            overall_level = self._score_to_consistency_level(overall_score)

            return VisualCriticResult(
                overall_consistency_score=overall_score,
                overall_consistency_level=overall_level,
                dimension_evaluations=dimension_evaluations,
                feedback_suggestions=feedback_suggestions,
                processing_time=0.0,
                comparison_confidence=0.5,
                metadata={
                    "original_image": str(original_path),
                    "rendered_image": str(rendered_path),
                    "evaluation_method": "fallback_basic",
                    "note": "Limited analysis without LLM"
                }
            )

        except Exception as e:
            raise VisualCriticError(f"Fallback evaluation failed: {str(e)}")

    def _calculate_size_similarity(self, img1: Image.Image, img2: Image.Image) -> float:
        """Calculate basic size similarity between images."""
        w1, h1 = img1.size
        w2, h2 = img2.size

        width_ratio = min(w1, w2) / max(w1, w2)
        height_ratio = min(h1, h2) / max(h1, h2)

        return (width_ratio + height_ratio) / 2

    def _calculate_basic_color_similarity(self, img1: Image.Image, img2: Image.Image) -> float:
        """Calculate basic color similarity between images."""
        try:
            # Convert to RGB and resize for comparison
            img1_rgb = img1.convert('RGB').resize((64, 64))
            img2_rgb = img2.convert('RGB').resize((64, 64))

            # Calculate average color difference
            pixels1 = list(img1_rgb.getdata())
            pixels2 = list(img2_rgb.getdata())

            total_diff = 0
            for p1, p2 in zip(pixels1, pixels2):
                diff = sum(abs(c1 - c2) for c1, c2 in zip(p1, p2))
                total_diff += diff

            # Normalize to 0-1 scale (lower is more similar)
            max_possible_diff = len(pixels1) * 3 * 255
            similarity = 1.0 - (total_diff / max_possible_diff)

            return max(0.0, min(1.0, similarity))

        except Exception:
            return 0.5  # Default similarity if calculation fails

    def _score_to_consistency_level(self, score: float) -> ConsistencyLevel:
        """Convert numeric score to consistency level."""
        if score >= 0.9:
            return ConsistencyLevel.EXCELLENT
        elif score >= 0.75:
            return ConsistencyLevel.GOOD
        elif score >= 0.5:
            return ConsistencyLevel.FAIR
        elif score >= 0.25:
            return ConsistencyLevel.POOR
        else:
            return ConsistencyLevel.VERY_POOR

    def _create_fallback_result(self, original_path: str, rendered_path: str) -> VisualCriticResult:
        """Create fallback result when parsing fails."""
        return VisualCriticResult(
            overall_consistency_score=0.5,
            overall_consistency_level=ConsistencyLevel.FAIR,
            dimension_evaluations=[],
            feedback_suggestions=[
                FeedbackSuggestion(
                    priority=FeedbackPriority.HIGH,
                    dimension=VisualDimension.SHAPE,
                    issue_description="Failed to parse LLM evaluation response",
                    suggested_correction="Review and improve prompt or response parsing",
                    actionable_steps=["Check LLM response format", "Verify JSON structure"],
                    specificity_score=2.0,
                    operability_score=3.0
                )
            ],
            processing_time=0.0,
            comparison_confidence=0.3,
            metadata={
                "original_image": original_path,
                "rendered_image": rendered_path,
                "evaluation_method": "fallback_error",
                "error": "Failed to parse LLM response"
            }
        )

    def generate_feedback_for_spec_agent(self,
                                        critic_result: VisualCriticResult,
                                        original_spec: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate structured feedback for SpecGenerationAgent to form outer loop.

        Args:
            critic_result: Visual criticism result
            original_spec: Original model specification that was used

        Returns:
            Structured feedback for SpecGenerationAgent
        """
        try:
            # Prioritize feedback suggestions
            critical_suggestions = [s for s in critic_result.feedback_suggestions
                                  if s.priority == FeedbackPriority.CRITICAL]
            high_suggestions = [s for s in critic_result.feedback_suggestions
                              if s.priority == FeedbackPriority.HIGH]

            # Create feedback structure
            feedback = {
                "feedback_type": "visual_critic",
                "timestamp": datetime.now().isoformat(),
                "overall_assessment": {
                    "consistency_score": critic_result.overall_consistency_score,
                    "consistency_level": critic_result.overall_consistency_level.value,
                    "confidence": critic_result.comparison_confidence
                },
                "critical_issues": [
                    {
                        "dimension": s.dimension.value,
                        "issue": s.issue_description,
                        "correction": s.suggested_correction,
                        "steps": s.actionable_steps
                    }
                    for s in critical_suggestions
                ],
                "improvement_suggestions": [
                    {
                        "dimension": s.dimension.value,
                        "priority": s.priority.value,
                        "issue": s.issue_description,
                        "correction": s.suggested_correction,
                        "specificity": s.specificity_score,
                        "operability": s.operability_score
                    }
                    for s in high_suggestions
                ],
                "dimension_scores": {
                    eval.dimension.value: {
                        "score": eval.score,
                        "level": eval.consistency_level.value,
                        "issues": eval.specific_issues
                    }
                    for eval in critic_result.dimension_evaluations
                },
                "requires_regeneration": critic_result.overall_consistency_score < 0.7,
                "metadata": critic_result.metadata
            }

            # Add original spec context if provided
            if original_spec:
                feedback["original_spec_context"] = original_spec

            logger.info(f"Generated feedback for SpecGenerationAgent: "
                       f"{len(critical_suggestions)} critical, {len(high_suggestions)} high priority issues")

            return feedback

        except Exception as e:
            logger.error(f"Failed to generate feedback for SpecGenerationAgent: {e}")
            raise VisualCriticError(f"Feedback generation failed: {str(e)}")

    def get_evaluation_summary(self, critic_result: VisualCriticResult) -> str:
        """Get human-readable evaluation summary."""
        summary_parts = [
            f"Visual Consistency Evaluation Summary",
            f"=" * 40,
            f"Overall Score: {critic_result.overall_consistency_score:.2f}/1.0",
            f"Overall Level: {critic_result.overall_consistency_level.value.title()}",
            f"Confidence: {critic_result.comparison_confidence:.2f}",
            f"Processing Time: {critic_result.processing_time:.2f}s",
            ""
        ]

        if critic_result.dimension_evaluations:
            summary_parts.append("Dimension Evaluations:")
            for eval in critic_result.dimension_evaluations:
                summary_parts.append(
                    f"  {eval.dimension.value.title()}: {eval.score:.2f} "
                    f"({eval.consistency_level.value})"
                )
            summary_parts.append("")

        if critic_result.feedback_suggestions:
            summary_parts.append("Key Feedback Suggestions:")
            for i, suggestion in enumerate(critic_result.feedback_suggestions[:3], 1):
                summary_parts.append(
                    f"  {i}. [{suggestion.priority.value.upper()}] "
                    f"{suggestion.dimension.value.title()}: {suggestion.issue_description}"
                )

        return "\n".join(summary_parts)
