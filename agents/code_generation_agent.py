"""
Code Generation Agent for Blender 3D Model Generation AI Agent System

This module implements a code generation agent that converts JSON specifications
conforming to base_model_spec.json into executable Blender Python (bpy) code.
It integrates with the Knowledge Agent for API assistance and includes static
code analysis using <PERSON>'s AST module.

Enhanced with Ray RLlib integration for optimal code generation strategy selection.

Author: Augment Agent
Date: 2025-07-18
"""

import os
import ast
import json
import logging
import uuid
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

import openai
from openai import OpenAI

# Import knowledge agent for API assistance
from agents.knowledge_agent import KnowledgeAgent, RetrievalResult, KnowledgeSource

# Ray RLlib imports (optional)
try:
    import ray
    import numpy as np
    from ray.rllib.algorithms.ppo import PPOConfig
    HAS_RAY = True
except ImportError:
    HAS_RAY = False
    print("Warning: Ray RLlib not available. Using fallback code generation strategies.")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CodeGenerationError(Exception):
    """Exception raised for code generation errors."""
    pass


class CodeQuality(Enum):
    """Code quality levels."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    INVALID = "invalid"


class CodeGenerationStrategy(Enum):
    """Code generation strategies for RL selection."""
    TEMPLATE_BASED = 0
    LLM_ASSISTED = 1
    HYBRID_APPROACH = 2
    KNOWLEDGE_GUIDED = 3
    ITERATIVE_REFINEMENT = 4


@dataclass
class CodeGenerationContext:
    """Context for RL-based strategy selection."""
    spec_complexity: float
    geometry_count: int
    has_materials: bool
    has_animations: bool
    knowledge_availability: float
    time_constraint: float
    quality_requirement: float


@dataclass
class CodeAnalysisResult:
    """Result of static code analysis."""
    syntax_valid: bool
    ast_parseable: bool
    quality_score: float
    quality_level: CodeQuality
    issues: List[str]
    suggestions: List[str]
    complexity_score: int
    line_count: int


@dataclass
class CodeGenerationConfig:
    """Configuration for code generation."""
    include_comments: bool = True
    include_error_handling: bool = True
    use_knowledge_agent: bool = True
    openai_model: str = "gpt-4"
    max_retries: int = 3
    code_style: str = "pep8"
    target_blender_version: str = "4.0"
    enable_static_analysis: bool = True


@dataclass
class CodeGenerationResult:
    """Result of code generation."""
    generated_code: str
    specification_used: Dict[str, Any]
    analysis_result: CodeAnalysisResult
    knowledge_context_used: List[str]
    generation_time: float
    confidence_score: float
    metadata: Optional[Dict[str, Any]] = None


class CodeGenerationAgent:
    """
    Code Generation Agent for converting 3D model specifications to Blender Python code.
    
    This agent takes JSON specifications conforming to base_model_spec.json and generates
    executable Blender Python (bpy) code with static analysis validation.
    """
    
    def __init__(self,
                 config: Optional[CodeGenerationConfig] = None,
                 knowledge_agent: Optional[KnowledgeAgent] = None,
                 openai_api_key: Optional[str] = None,
                 enable_rl: bool = True):
        """
        Initialize the Code Generation Agent.

        Args:
            config: Configuration for the agent
            knowledge_agent: Knowledge agent for API assistance
            openai_api_key: OpenAI API key for LLM calls
            enable_rl: Whether to enable RL-based strategy selection
        """
        self.config = config or CodeGenerationConfig()
        self.knowledge_agent = knowledge_agent
        self.enable_rl = enable_rl and HAS_RAY

        # Initialize OpenAI client
        api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
        if api_key:
            self.openai_client = OpenAI(api_key=api_key)
        else:
            logger.warning("No OpenAI API key provided. LLM-based generation will not be available.")
            self.openai_client = None

        # Initialize RL components
        self._init_rl_components()

        # Strategy performance tracking
        self.strategy_performance_history = {strategy: [] for strategy in CodeGenerationStrategy}
        self.generation_success_rate = 0.0

        # Load code templates
        self.code_templates = self._load_code_templates()

        logger.info("CodeGenerationAgent initialized successfully")

    def _init_rl_components(self):
        """Initialize RL components for strategy selection."""
        if not self.enable_rl:
            logger.info("RL disabled, using heuristic strategy selection")
            self.rl_policy = None
            return

        try:
            # Initialize Ray if not already done
            if not ray.is_initialized():
                ray.init(ignore_reinit_error=True)

            # Create RL environment for code generation strategy selection
            from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv

            # Configure PPO for strategy selection
            self.rl_config = (
                PPOConfig()
                .environment(MinimalBlenderTaskEnv)
                .framework("torch")
                .training(
                    lr=0.0001,
                    num_sgd_iter=5,
                    sgd_minibatch_size=32,
                    train_batch_size=500,
                )
                .resources(num_gpus=0)
            )

            # Build algorithm
            self.rl_policy = self.rl_config.build()
            logger.info("RL policy initialized for code generation strategy selection")

        except Exception as e:
            logger.warning(f"Failed to initialize RL components: {e}")
            self.enable_rl = False
            self.rl_policy = None

    def _select_generation_strategy(self, context: CodeGenerationContext) -> CodeGenerationStrategy:
        """
        Select optimal code generation strategy using RL policy or heuristics.

        Args:
            context: Context for strategy selection

        Returns:
            Selected code generation strategy
        """
        if self.enable_rl and self.rl_policy:
            try:
                # Create state vector for RL policy
                state = np.array([
                    context.spec_complexity,
                    min(1.0, context.geometry_count / 10.0),  # Normalized geometry count
                    1.0 if context.has_materials else 0.0,
                    1.0 if context.has_animations else 0.0,
                    context.knowledge_availability,
                    context.time_constraint,
                    context.quality_requirement
                ], dtype=np.float32)

                # Get action from RL policy
                action = self.rl_policy.compute_single_action(state)

                # Map action to strategy
                if action < len(CodeGenerationStrategy):
                    selected_strategy = list(CodeGenerationStrategy)[action]
                    logger.debug(f"RL selected strategy: {selected_strategy.name}")
                    return selected_strategy

            except Exception as e:
                logger.warning(f"RL strategy selection failed: {e}, falling back to heuristics")

        # Fallback to heuristic selection
        return self._heuristic_strategy_selection(context)

    def _heuristic_strategy_selection(self, context: CodeGenerationContext) -> CodeGenerationStrategy:
        """Fallback heuristic strategy selection."""
        # Simple heuristics based on context
        if context.spec_complexity > 0.8 and context.knowledge_availability > 0.7:
            return CodeGenerationStrategy.KNOWLEDGE_GUIDED
        elif context.geometry_count > 5 or context.has_animations:
            return CodeGenerationStrategy.ITERATIVE_REFINEMENT
        elif self.openai_client and context.quality_requirement > 0.7:
            return CodeGenerationStrategy.LLM_ASSISTED
        elif context.time_constraint > 0.7:
            return CodeGenerationStrategy.TEMPLATE_BASED
        else:
            return CodeGenerationStrategy.HYBRID_APPROACH

    def _calculate_strategy_reward(self, strategy: CodeGenerationStrategy,
                                  generation_success: bool,
                                  code_quality: CodeQuality,
                                  generation_time: float,
                                  execution_success: bool = None) -> float:
        """
        Calculate reward for strategy selection to update RL policy.

        Args:
            strategy: Strategy that was used
            generation_success: Whether code generation was successful
            code_quality: Quality of generated code
            generation_time: Time taken to generate code
            execution_success: Whether generated code executed successfully

        Returns:
            Calculated reward value
        """
        # Base reward for success/failure
        base_reward = 8.0 if generation_success else -3.0

        # Quality bonus
        quality_scores = {
            CodeQuality.EXCELLENT: 5.0,
            CodeQuality.GOOD: 3.0,
            CodeQuality.ACCEPTABLE: 1.0,
            CodeQuality.POOR: -1.0,
            CodeQuality.INVALID: -3.0
        }
        quality_bonus = quality_scores.get(code_quality, 0.0)

        # Efficiency bonus (faster is better, but not at cost of quality)
        efficiency_bonus = max(0, 3.0 - generation_time)

        # Execution success bonus
        execution_bonus = 3.0 if execution_success else -2.0 if execution_success is False else 0.0

        # Strategy-specific bonuses
        strategy_bonus = 0.0
        if strategy == CodeGenerationStrategy.TEMPLATE_BASED and generation_time < 1.0:
            strategy_bonus = 1.0
        elif strategy == CodeGenerationStrategy.LLM_ASSISTED and code_quality in [CodeQuality.EXCELLENT, CodeQuality.GOOD]:
            strategy_bonus = 2.0
        elif strategy == CodeGenerationStrategy.KNOWLEDGE_GUIDED and execution_success:
            strategy_bonus = 1.5

        total_reward = base_reward + quality_bonus + efficiency_bonus + execution_bonus + strategy_bonus

        # Update performance history
        self.strategy_performance_history[strategy].append(total_reward)
        if len(self.strategy_performance_history[strategy]) > 100:
            self.strategy_performance_history[strategy].pop(0)  # Keep last 100 records

        return total_reward

    def _load_code_templates(self) -> Dict[str, str]:
        """Load Blender Python code templates for different geometry types."""
        templates = {
            "header": '''import bpy
import bmesh
from mathutils import Vector

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Clear existing materials
for material in bpy.data.materials:
    bpy.data.materials.remove(material)

''',
            "cube": '''
# Create cube: {name}
bpy.ops.mesh.primitive_cube_add(
    size={size},
    location=({pos_x}, {pos_y}, {pos_z})
)
cube_obj = bpy.context.active_object
cube_obj.name = "{name}"
cube_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
cube_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "sphere": '''
# Create sphere: {name}
bpy.ops.mesh.primitive_uv_sphere_add(
    radius={radius},
    location=({pos_x}, {pos_y}, {pos_z}),
    subdivisions={subdivisions}
)
sphere_obj = bpy.context.active_object
sphere_obj.name = "{name}"
sphere_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
sphere_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "cylinder": '''
# Create cylinder: {name}
bpy.ops.mesh.primitive_cylinder_add(
    radius={radius},
    depth={height},
    location=({pos_x}, {pos_y}, {pos_z}),
    vertices={vertices}
)
cylinder_obj = bpy.context.active_object
cylinder_obj.name = "{name}"
cylinder_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
cylinder_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "plane": '''
# Create plane: {name}
bpy.ops.mesh.primitive_plane_add(
    size={size},
    location=({pos_x}, {pos_y}, {pos_z})
)
plane_obj = bpy.context.active_object
plane_obj.name = "{name}"
plane_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
plane_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "cone": '''
# Create cone: {name}
bpy.ops.mesh.primitive_cone_add(
    radius1={radius},
    depth={height},
    location=({pos_x}, {pos_y}, {pos_z}),
    vertices={vertices}
)
cone_obj = bpy.context.active_object
cone_obj.name = "{name}"
cone_obj.rotation_euler = ({rot_x}, {rot_y}, {rot_z})
cone_obj.scale = ({scale_x}, {scale_y}, {scale_z})
''',
            "material": '''
# Create material: {material_name}
material = bpy.data.materials.new(name="{material_name}")
material.use_nodes = True
bsdf = material.node_tree.nodes["Principled BSDF"]
bsdf.inputs[0].default_value = ({r}, {g}, {b}, {a})  # Base Color
{material_obj}.data.materials.append(material)
''',
            "footer": '''
# Update scene
bpy.context.view_layer.update()

# Optional: Save the file
# bpy.ops.wm.save_as_mainfile(filepath="/path/to/output.blend")
'''
        }
        return templates
    
    def generate_blender_code(self,
                             specification: Dict[str, Any],
                             output_path: Optional[str] = None,
                             enable_rl_strategy: bool = True) -> CodeGenerationResult:
        """
        Generate Blender Python code from a JSON specification with RL-enhanced strategy selection.

        Args:
            specification: JSON specification conforming to base_model_spec.json
            output_path: Optional path to save the generated code
            enable_rl_strategy: Whether to use RL for strategy selection

        Returns:
            CodeGenerationResult containing the generated code and analysis
        """
        start_time = time.time()

        try:
            # Validate input specification
            self._validate_specification(specification)

            # Get knowledge context if available
            knowledge_context = []
            if self.config.use_knowledge_agent and self.knowledge_agent:
                knowledge_context = self._get_knowledge_context(specification)

            # Create context for RL strategy selection
            if enable_rl_strategy and self.enable_rl:
                context = CodeGenerationContext(
                    spec_complexity=self._calculate_spec_complexity(specification),
                    geometry_count=len(specification.get('objects', [])),
                    has_materials=any('material' in obj for obj in specification.get('objects', [])),
                    has_animations=any('animation' in obj for obj in specification.get('objects', [])),
                    knowledge_availability=len(knowledge_context) / 10.0,  # Normalized
                    time_constraint=0.5,  # Default medium urgency
                    quality_requirement=0.8  # Default high quality
                )

                selected_strategy = self._select_generation_strategy(context)
                logger.debug(f"Selected strategy for spec: {selected_strategy.name}")
            else:
                selected_strategy = CodeGenerationStrategy.TEMPLATE_BASED  # Default

            # Generate code using selected strategy
            generated_code = self._execute_generation_strategy(
                specification, knowledge_context, selected_strategy
            )

            # Perform static code analysis
            analysis_result = self._analyze_code(generated_code)

            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(analysis_result, knowledge_context)

            # Calculate performance metrics
            generation_time = time.time() - start_time
            generation_success = analysis_result.syntax_valid and analysis_result.quality_level != CodeQuality.INVALID

            # Update RL policy with reward
            if enable_rl_strategy and self.enable_rl:
                reward = self._calculate_strategy_reward(
                    selected_strategy, generation_success, analysis_result.quality_level, generation_time
                )
                logger.debug(f"Strategy {selected_strategy.name} reward: {reward:.2f}")

            # Update success rate
            self.generation_success_rate = 0.9 * self.generation_success_rate + 0.1 * (1.0 if generation_success else 0.0)

            # Save code if output path provided
            if output_path:
                self._save_code(generated_code, output_path)

            return CodeGenerationResult(
                generated_code=generated_code,
                specification_used=specification,
                analysis_result=analysis_result,
                knowledge_context_used=[ctx.chunk.content[:100] + "..." for ctx in knowledge_context],
                generation_time=generation_time,
                confidence_score=confidence_score,
                metadata={
                    'objects_count': len(specification.get('objects', [])),
                    'generation_method': selected_strategy.name,
                    'blender_version': self.config.target_blender_version,
                    'strategy_used': selected_strategy.name
                }
            )
            
        except Exception as e:
            logger.error(f"Code generation failed: {e}")
            raise CodeGenerationError(f"Failed to generate code: {e}")

    def _calculate_spec_complexity(self, specification: Dict[str, Any]) -> float:
        """Calculate complexity score for the specification."""
        complexity = 0.0

        # Base complexity from object count
        object_count = len(specification.get('objects', []))
        complexity += min(1.0, object_count / 10.0)

        # Additional complexity factors
        for obj in specification.get('objects', []):
            # Material complexity
            if 'material' in obj:
                complexity += 0.1

            # Animation complexity
            if 'animation' in obj:
                complexity += 0.2

            # Complex geometry
            geometry_type = obj.get('geometry', {}).get('type', '')
            if geometry_type in ['mesh', 'nurbs', 'curve']:
                complexity += 0.15

        return min(1.0, complexity)

    def _execute_generation_strategy(self, specification: Dict[str, Any],
                                   knowledge_context: List[RetrievalResult],
                                   strategy: CodeGenerationStrategy) -> str:
        """Execute code generation using the selected strategy."""

        if strategy == CodeGenerationStrategy.TEMPLATE_BASED:
            return self._generate_code_from_template(specification, knowledge_context)

        elif strategy == CodeGenerationStrategy.LLM_ASSISTED:
            if self.openai_client:
                return self.generate_code_with_llm(specification, knowledge_context)
            else:
                logger.warning("LLM not available, falling back to template-based")
                return self._generate_code_from_template(specification, knowledge_context)

        elif strategy == CodeGenerationStrategy.KNOWLEDGE_GUIDED:
            return self._generate_knowledge_guided_code(specification, knowledge_context)

        elif strategy == CodeGenerationStrategy.HYBRID_APPROACH:
            return self._generate_hybrid_code(specification, knowledge_context)

        elif strategy == CodeGenerationStrategy.ITERATIVE_REFINEMENT:
            return self._generate_iterative_code(specification, knowledge_context)

        else:
            # Default fallback
            return self._generate_code_from_template(specification, knowledge_context)

    def _generate_knowledge_guided_code(self, specification: Dict[str, Any],
                                       knowledge_context: List[RetrievalResult]) -> str:
        """Generate code with heavy knowledge base guidance."""
        # Start with template
        base_code = self._generate_code_from_template(specification, knowledge_context)

        # Enhance with knowledge context
        if knowledge_context:
            # Add relevant API calls from knowledge base
            enhancements = []
            for ctx in knowledge_context[:3]:  # Use top 3 results
                if 'bpy.' in ctx.chunk.content:
                    # Extract API calls
                    lines = ctx.chunk.content.split('\n')
                    for line in lines:
                        if 'bpy.' in line and not line.strip().startswith('#'):
                            enhancements.append(f"# From knowledge base: {line.strip()}")

            if enhancements:
                base_code += "\n\n# Knowledge-guided enhancements:\n"
                base_code += "\n".join(enhancements[:5])  # Limit to 5 enhancements

        return base_code

    def _generate_hybrid_code(self, specification: Dict[str, Any],
                             knowledge_context: List[RetrievalResult]) -> str:
        """Generate code using hybrid template + LLM approach."""
        # Start with template
        template_code = self._generate_code_from_template(specification, knowledge_context)

        # Enhance with LLM if available
        if self.openai_client and len(specification.get('objects', [])) > 3:
            try:
                llm_code = self.generate_code_with_llm(specification, knowledge_context)
                # Combine both approaches
                return f"{template_code}\n\n# LLM-enhanced additions:\n{llm_code}"
            except Exception as e:
                logger.warning(f"LLM enhancement failed: {e}")

        return template_code

    def _generate_iterative_code(self, specification: Dict[str, Any],
                                knowledge_context: List[RetrievalResult]) -> str:
        """Generate code with iterative refinement."""
        # Start with basic template
        code = self._generate_code_from_template(specification, knowledge_context)

        # Iteratively refine based on complexity
        for iteration in range(2):  # Max 2 refinement iterations
            # Analyze current code
            analysis = self._analyze_code(code)

            # Add refinements based on analysis
            if analysis.quality in [CodeQuality.POOR, CodeQuality.ACCEPTABLE]:
                # Add error handling
                code += "\n\n# Error handling refinement\n"
                code += "try:\n    bpy.ops.object.select_all(action='DESELECT')\nexcept:\n    pass\n"

            # Add optimization hints
            if len(specification.get('objects', [])) > 5:
                code += "\n\n# Performance optimization\n"
                code += "bpy.context.view_layer.update()\n"

        return code

    def _validate_specification(self, specification: Dict[str, Any]):
        """Validate the input specification."""
        required_fields = ['schema_version', 'model_info', 'objects']
        for field in required_fields:
            if field not in specification:
                raise CodeGenerationError(f"Missing required field: {field}")
        
        if not isinstance(specification['objects'], list):
            raise CodeGenerationError("Objects field must be a list")
        
        if len(specification['objects']) == 0:
            raise CodeGenerationError("Objects list cannot be empty")
        
        # Validate each object
        for i, obj in enumerate(specification['objects']):
            if 'geometry' not in obj:
                raise CodeGenerationError(f"Object {i} missing geometry field")

            geometry_type = obj['geometry'].get('type')
            if geometry_type not in ['cube', 'sphere', 'cylinder', 'plane', 'cone']:
                raise CodeGenerationError(f"Unsupported geometry type: {geometry_type}")

    def _get_knowledge_context(self, specification: Dict[str, Any]) -> List[RetrievalResult]:
        """Get relevant knowledge context for code generation."""
        if not self.knowledge_agent:
            return []

        try:
            # Build query from geometry types
            geometry_types = []
            for obj in specification.get('objects', []):
                geometry_type = obj.get('geometry', {}).get('type')
                if geometry_type:
                    geometry_types.append(geometry_type)

            query = f"blender python bpy create {' '.join(set(geometry_types))} mesh primitive"

            # Query knowledge base
            results = self.knowledge_agent.query_knowledge(query, top_k=5)
            logger.info(f"Retrieved {len(results)} knowledge context items")
            return results

        except Exception as e:
            logger.warning(f"Failed to get knowledge context: {e}")
            return []

    def _generate_code_from_template(self,
                                   specification: Dict[str, Any],
                                   knowledge_context: List[RetrievalResult]) -> str:
        """Generate Blender Python code using templates."""
        code_parts = []

        # Add header
        code_parts.append(self.code_templates["header"])

        # Add comment with model info
        model_info = specification.get('model_info', {})
        if self.config.include_comments:
            code_parts.append(f'# Generated model: {model_info.get("name", "Unnamed")}\n')
            code_parts.append(f'# Description: {model_info.get("description", "No description")}\n')
            code_parts.append(f'# Generated at: {datetime.now().isoformat()}\n\n')

        # Process each object
        for obj in specification.get('objects', []):
            object_code = self._generate_object_code(obj)
            code_parts.append(object_code)

        # Add footer
        code_parts.append(self.code_templates["footer"])

        return ''.join(code_parts)

    def _generate_object_code(self, obj: Dict[str, Any]) -> str:
        """Generate code for a single object."""
        geometry = obj.get('geometry', {})
        geometry_type = geometry.get('type')

        # Get object properties with defaults
        name = obj.get('name', f'Object_{uuid.uuid4().hex[:8]}')
        transform = obj.get('transform', {})
        material = obj.get('material', {})

        # Extract transform properties with defaults
        position = transform.get('position', {'x': 0.0, 'y': 0.0, 'z': 0.0})
        rotation = transform.get('rotation', {'x': 0.0, 'y': 0.0, 'z': 0.0})
        scale = transform.get('scale', {'x': 1.0, 'y': 1.0, 'z': 1.0})

        # Prepare template parameters
        params = {
            'name': name,
            'pos_x': position.get('x', 0.0),
            'pos_y': position.get('y', 0.0),
            'pos_z': position.get('z', 0.0),
            'rot_x': rotation.get('x', 0.0),
            'rot_y': rotation.get('y', 0.0),
            'rot_z': rotation.get('z', 0.0),
            'scale_x': scale.get('x', 1.0),
            'scale_y': scale.get('y', 1.0),
            'scale_z': scale.get('z', 1.0)
        }

        # Add geometry-specific parameters
        if geometry_type == 'cube':
            params['size'] = geometry.get('size', 2.0)
        elif geometry_type == 'sphere':
            params['radius'] = geometry.get('radius', 1.0)
            params['subdivisions'] = geometry.get('subdivisions', 4)
        elif geometry_type == 'cylinder':
            params['radius'] = geometry.get('radius', 1.0)
            params['height'] = geometry.get('height', 2.0)
            params['vertices'] = geometry.get('vertices', 32)
        elif geometry_type == 'plane':
            params['size'] = geometry.get('size', 2.0)
        elif geometry_type == 'cone':
            params['radius'] = geometry.get('radius', 1.0)
            params['height'] = geometry.get('height', 2.0)
            params['vertices'] = geometry.get('vertices', 32)

        # Generate geometry code
        geometry_code = self.code_templates[geometry_type].format(**params)

        # Generate material code if material is specified
        material_code = ""
        if material and material.get('type'):
            material_params = {
                'material_name': material.get('name', f'{name}_material'),
                'material_obj': f'{geometry_type}_obj',
                'r': material.get('color', {}).get('r', 0.8),
                'g': material.get('color', {}).get('g', 0.8),
                'b': material.get('color', {}).get('b', 0.8),
                'a': material.get('color', {}).get('a', 1.0)
            }
            material_code = self.code_templates["material"].format(**material_params)

        return geometry_code + material_code + "\n"

    def _analyze_code(self, code: str) -> CodeAnalysisResult:
        """Perform static code analysis using AST."""
        issues = []
        suggestions = []
        syntax_valid = False
        ast_parseable = False
        complexity_score = 0
        line_count = len(code.split('\n'))

        try:
            # Parse code with AST
            tree = ast.parse(code)
            ast_parseable = True
            syntax_valid = True

            # Calculate complexity score (simple metric based on node count)
            complexity_score = self._calculate_complexity(tree)

            # Check for common issues
            issues.extend(self._check_code_issues(tree, code))

            # Generate suggestions
            suggestions.extend(self._generate_suggestions(tree, code))

        except SyntaxError as e:
            issues.append(f"Syntax error: {e}")
            syntax_valid = False
            ast_parseable = False
        except Exception as e:
            issues.append(f"Analysis error: {e}")

        # Calculate quality score
        quality_score = self._calculate_quality_score(syntax_valid, ast_parseable, issues, complexity_score)
        quality_level = self._determine_quality_level(quality_score)

        return CodeAnalysisResult(
            syntax_valid=syntax_valid,
            ast_parseable=ast_parseable,
            quality_score=quality_score,
            quality_level=quality_level,
            issues=issues,
            suggestions=suggestions,
            complexity_score=complexity_score,
            line_count=line_count
        )

    def _calculate_complexity(self, tree: ast.AST) -> int:
        """Calculate code complexity based on AST nodes."""
        complexity = 0
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.For, ast.While, ast.Try)):
                complexity += 1
            elif isinstance(node, ast.FunctionDef):
                complexity += 2
            elif isinstance(node, ast.ClassDef):
                complexity += 3
        return complexity

    def _check_code_issues(self, tree: ast.AST, code: str) -> List[str]:
        """Check for common code issues."""
        issues = []

        # Check for hardcoded values that should be parameterized
        lines = code.split('\n')
        for i, line in enumerate(lines):
            if 'filepath=' in line and '/path/to/' in line:
                issues.append(f"Line {i+1}: Hardcoded file path should be parameterized")

        # Check for missing error handling around bpy operations
        has_error_handling = any(isinstance(node, ast.Try) for node in ast.walk(tree))
        if not has_error_handling and self.config.include_error_handling:
            issues.append("Consider adding error handling for Blender operations")

        return issues

    def _generate_suggestions(self, tree: ast.AST, code: str) -> List[str]:
        """Generate code improvement suggestions."""
        suggestions = []

        # Count function calls to suggest optimization
        bpy_calls = 0
        for node in ast.walk(tree):
            if isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute):
                if isinstance(node.func.value, ast.Name) and node.func.value.id == 'bpy':
                    bpy_calls += 1

        if bpy_calls > 10:
            suggestions.append("Consider batching Blender operations for better performance")

        # Check for documentation
        has_docstring = False
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and ast.get_docstring(node):
                has_docstring = True
                break

        if not has_docstring and self.config.include_comments:
            suggestions.append("Consider adding function documentation")

        return suggestions

    def _calculate_quality_score(self, syntax_valid: bool, ast_parseable: bool,
                                issues: List[str], complexity_score: int) -> float:
        """Calculate overall code quality score (0.0 to 1.0)."""
        if not syntax_valid:
            return 0.0

        if not ast_parseable:
            return 0.1

        # Base score for valid syntax
        score = 0.7

        # Deduct points for issues
        score -= len(issues) * 0.05

        # Adjust for complexity (moderate complexity is good)
        if complexity_score < 5:
            score += 0.1  # Simple is good
        elif complexity_score > 20:
            score -= 0.1  # Too complex

        return max(0.0, min(1.0, score))

    def _determine_quality_level(self, quality_score: float) -> CodeQuality:
        """Determine quality level from score."""
        if quality_score >= 0.9:
            return CodeQuality.EXCELLENT
        elif quality_score >= 0.8:
            return CodeQuality.GOOD
        elif quality_score >= 0.6:
            return CodeQuality.ACCEPTABLE
        elif quality_score >= 0.3:
            return CodeQuality.POOR
        else:
            return CodeQuality.INVALID

    def _calculate_confidence_score(self, analysis_result: CodeAnalysisResult,
                                  knowledge_context: List[RetrievalResult]) -> float:
        """Calculate confidence score for the generated code."""
        confidence = 0.5  # Base confidence

        # Boost confidence for valid syntax and AST
        if analysis_result.syntax_valid:
            confidence += 0.2
        if analysis_result.ast_parseable:
            confidence += 0.1

        # Boost confidence based on quality score
        confidence += analysis_result.quality_score * 0.2

        # Boost confidence if we have knowledge context
        if knowledge_context:
            confidence += min(len(knowledge_context) * 0.02, 0.1)

        # Reduce confidence for issues
        confidence -= len(analysis_result.issues) * 0.05

        return max(0.0, min(1.0, confidence))

    def _save_code(self, code: str, output_path: str):
        """Save generated code to file."""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(code)

            logger.info(f"Code saved to: {output_path}")

        except Exception as e:
            logger.error(f"Failed to save code: {e}")
            raise CodeGenerationError(f"Failed to save code: {e}")

    def validate_code_only(self, code: str) -> CodeAnalysisResult:
        """
        Validate code without generating it.

        Args:
            code: Python code to validate

        Returns:
            CodeAnalysisResult with validation results
        """
        return self._analyze_code(code)

    def get_supported_geometry_types(self) -> List[str]:
        """Get list of supported geometry types."""
        return ['cube', 'sphere', 'cylinder', 'plane', 'cone']

    def get_code_template(self, geometry_type: str) -> str:
        """Get code template for a specific geometry type."""
        if geometry_type not in self.code_templates:
            raise CodeGenerationError(f"Unsupported geometry type: {geometry_type}")
        return self.code_templates[geometry_type]

    def generate_code_with_llm(self,
                              specification: Dict[str, Any],
                              knowledge_context: List[RetrievalResult]) -> str:
        """
        Generate code using LLM (fallback method).

        Args:
            specification: JSON specification
            knowledge_context: Knowledge context from KnowledgeAgent

        Returns:
            Generated Python code
        """
        if not self.openai_client:
            raise CodeGenerationError("OpenAI client not available for LLM generation")

        # Prepare context for LLM
        context_text = self._prepare_llm_context(specification, knowledge_context)

        # Create prompt for code generation
        prompt = self._create_code_generation_prompt(context_text)

        try:
            response = self.openai_client.chat.completions.create(
                model=self.config.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert Blender Python developer."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )

            generated_code = response.choices[0].message.content.strip()

            # Extract code from markdown if present
            if "```python" in generated_code:
                start = generated_code.find("```python") + 9
                end = generated_code.find("```", start)
                if end != -1:
                    generated_code = generated_code[start:end].strip()

            return generated_code

        except Exception as e:
            logger.error(f"LLM code generation failed: {e}")
            raise CodeGenerationError(f"LLM generation failed: {e}")

    def _prepare_llm_context(self, specification: Dict[str, Any],
                           knowledge_context: List[RetrievalResult]) -> str:
        """Prepare context for LLM code generation."""
        context_parts = []

        # Add specification info
        context_parts.append("SPECIFICATION:")
        context_parts.append(json.dumps(specification, indent=2))

        # Add knowledge context
        if knowledge_context:
            context_parts.append("\nRELEVANT BLENDER API KNOWLEDGE:")
            for i, result in enumerate(knowledge_context[:3]):
                context_parts.append(f"{i+1}. {result.chunk.content[:300]}...")

        return "\n".join(context_parts)

    def _create_code_generation_prompt(self, context_text: str) -> str:
        """Create prompt for LLM code generation."""
        return f"""Generate Blender Python (bpy) code based on the following specification:

{context_text}

REQUIREMENTS:
1. Generate clean, executable Blender Python code
2. Use proper bpy API calls for creating geometry
3. Include proper object naming and transformations
4. Add materials if specified in the specification
5. Include error handling where appropriate
6. Follow PEP 8 style guidelines
7. Add helpful comments

Generate the complete Python script:"""
