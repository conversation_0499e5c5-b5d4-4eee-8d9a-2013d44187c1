"""
Demo script for Task 3.3: Ray RLlib基础集成、奖励函数设计与RL环境构建

This script demonstrates the RL-enhanced functionality in KnowledgeAgent and 
CodeGenerationAgent, showing how they use reinforcement learning for optimal
tool and strategy selection.
"""

import os
import sys
import json
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(__file__))

from agents.knowledge_agent import (
    KnowledgeAgent, KnowledgeToolType, ToolSelectionContext
)
from agents.code_generation_agent import (
    CodeGenerationAgent, CodeGenerationStrategy, CodeGenerationContext
)
from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv, TaskType, ToolType


def demo_knowledge_agent_rl():
    """Demonstrate RL-enhanced knowledge agent functionality."""
    print("=== Knowledge Agent RL Integration Demo ===\n")
    
    # Initialize knowledge agent with RL enabled
    knowledge_agent = KnowledgeAgent(enable_rl=True)
    
    # Load knowledge base
    print("Loading knowledge base...")
    success = knowledge_agent.load_knowledge_base()
    if success:
        print("✅ Knowledge base loaded successfully")
    else:
        print("⚠️ Knowledge base loading failed, continuing with demo")
    
    # Demo queries with different characteristics
    test_queries = [
        {
            "query": "blender python create cube mesh",
            "description": "Blender API specific query"
        },
        {
            "query": "molecular nodes protein structure",
            "description": "MCP specific query"
        },
        {
            "query": "3d modeling best practices",
            "description": "General knowledge query"
        },
        {
            "query": "bpy.ops.mesh.primitive_cube_add parameters",
            "description": "Complex API query"
        }
    ]
    
    print("\n--- Testing RL-Enhanced Tool Selection ---")
    for i, test in enumerate(test_queries, 1):
        print(f"\n{i}. Query: '{test['query']}'")
        print(f"   Type: {test['description']}")
        
        start_time = time.time()
        
        # Query with RL tool selection enabled
        results = knowledge_agent.query_knowledge(
            query=test['query'],
            top_k=3,
            enable_rl_selection=True
        )
        
        query_time = time.time() - start_time
        
        print(f"   Results: {len(results)} items found")
        print(f"   Query time: {query_time:.3f}s")
        
        if results:
            avg_relevance = sum(r.relevance_score for r in results) / len(results)
            print(f"   Avg relevance: {avg_relevance:.3f}")
        
        # Show tool performance history
        print("   Tool performance summary:")
        for tool, history in knowledge_agent.tool_performance_history.items():
            if history:
                avg_reward = sum(history) / len(history)
                print(f"     {tool.name}: {len(history)} uses, avg reward: {avg_reward:.2f}")
    
    print(f"\nOverall query success rate: {knowledge_agent.query_success_rate:.3f}")


def demo_code_generation_agent_rl():
    """Demonstrate RL-enhanced code generation agent functionality."""
    print("\n\n=== Code Generation Agent RL Integration Demo ===\n")
    
    # Initialize code generation agent with RL enabled
    code_agent = CodeGenerationAgent(enable_rl=True)
    
    # Test specifications with different complexity levels
    test_specs = [
        {
            "name": "Simple Cube",
            "spec": {
                'schema_version': '1.0',
                'model_info': {'name': 'simple_cube'},
                'objects': [
                    {'geometry': {'type': 'cube'}}
                ]
            }
        },
        {
            "name": "Complex Scene",
            "spec": {
                'schema_version': '1.0',
                'model_info': {'name': 'complex_scene'},
                'objects': [
                    {
                        'geometry': {'type': 'cube'},
                        'material': {'type': 'pbr', 'color': [1, 0, 0]}
                    },
                    {
                        'geometry': {'type': 'sphere'},
                        'material': {'type': 'basic'}
                    },
                    {
                        'geometry': {'type': 'cylinder'},
                        'animation': {'type': 'rotation'}
                    }
                ]
            }
        },
        {
            "name": "Material Heavy",
            "spec": {
                'schema_version': '1.0',
                'model_info': {'name': 'material_test'},
                'objects': [
                    {
                        'geometry': {'type': 'sphere'},
                        'material': {'type': 'pbr', 'metallic': 0.8, 'roughness': 0.2}
                    },
                    {
                        'geometry': {'type': 'cube'},
                        'material': {'type': 'emission', 'strength': 2.0}
                    }
                ]
            }
        }
    ]
    
    print("--- Testing RL-Enhanced Strategy Selection ---")
    for i, test in enumerate(test_specs, 1):
        print(f"\n{i}. Specification: {test['name']}")
        print(f"   Objects: {len(test['spec']['objects'])}")
        
        # Calculate complexity
        complexity = code_agent._calculate_spec_complexity(test['spec'])
        print(f"   Complexity: {complexity:.3f}")
        
        start_time = time.time()
        
        try:
            # Generate code with RL strategy selection
            result = code_agent.generate_blender_code(
                specification=test['spec'],
                enable_rl_strategy=True
            )
            
            generation_time = time.time() - start_time
            
            print(f"   Generation time: {generation_time:.3f}s")
            print(f"   Strategy used: {result.metadata.get('strategy_used', 'unknown')}")
            print(f"   Code quality: {result.analysis_result.quality_level.value}")
            print(f"   Confidence: {result.confidence_score:.3f}")
            print(f"   Code lines: {result.analysis_result.line_count}")
            
            # Show first few lines of generated code
            code_lines = result.generated_code.split('\n')[:5]
            print("   Code preview:")
            for line in code_lines:
                if line.strip():
                    print(f"     {line}")
            
        except Exception as e:
            print(f"   ❌ Generation failed: {e}")
    
    # Show strategy performance summary
    print("\n--- Strategy Performance Summary ---")
    for strategy, history in code_agent.strategy_performance_history.items():
        if history:
            avg_reward = sum(history) / len(history)
            print(f"{strategy.name}: {len(history)} uses, avg reward: {avg_reward:.2f}")
    
    print(f"\nOverall generation success rate: {code_agent.generation_success_rate:.3f}")


def demo_rl_environment():
    """Demonstrate RL environment functionality."""
    print("\n\n=== RL Environment Demo ===\n")
    
    env = MinimalBlenderTaskEnv()
    
    print("--- Running RL Environment Episodes ---")
    
    for episode in range(3):
        print(f"\nEpisode {episode + 1}:")
        
        observation, info = env.reset()
        print(f"  Initial task: {env.current_task.name}")
        print(f"  Initial state: {observation}")
        
        total_reward = 0
        steps = 0
        max_steps = 10
        
        while steps < max_steps:
            # Select action based on current task (simple heuristic)
            if env.current_task == TaskType.IMAGE_ANALYSIS:
                action = ToolType.ANALYZE_IMAGE.value
            elif env.current_task == TaskType.SPEC_GENERATION:
                action = ToolType.GENERATE_SPEC.value
            elif env.current_task == TaskType.CODE_GENERATION:
                action = ToolType.GENERATE_CODE.value
            elif env.current_task == TaskType.BLENDER_EXECUTION:
                action = ToolType.EXECUTE_BLENDER.value
            else:
                action = ToolType.VALIDATE_OUTPUT.value
            
            observation, reward, terminated, truncated, info = env.step(action)
            
            total_reward += reward
            steps += 1
            
            print(f"    Step {steps}: Action={info['selected_tool']}, Reward={reward:.2f}, Progress={info['task_progress']:.1f}")
            
            if terminated or truncated:
                print(f"    Episode ended: {'Completed' if terminated else 'Truncated'}")
                break
        
        print(f"  Total reward: {total_reward:.2f}")
        print(f"  Final progress: {env.task_progress:.1f}/10.0")


def demo_reward_function_validation():
    """Demonstrate reward function validation."""
    print("\n\n=== Reward Function Validation ===\n")
    
    # Test knowledge agent reward calculation
    knowledge_agent = KnowledgeAgent(enable_rl=False)
    
    print("--- Knowledge Agent Reward Tests ---")
    test_cases = [
        {"tool": KnowledgeToolType.VECTOR_SEARCH, "success": True, "relevance": 0.9, "time": 1.0},
        {"tool": KnowledgeToolType.BLENDER_API_LOOKUP, "success": True, "relevance": 0.8, "time": 0.5},
        {"tool": KnowledgeToolType.KEYWORD_SEARCH, "success": False, "relevance": 0.2, "time": 3.0},
    ]
    
    for i, case in enumerate(test_cases, 1):
        reward = knowledge_agent._calculate_tool_reward(
            tool=case["tool"],
            query_success=case["success"],
            relevance_score=case["relevance"],
            response_time=case["time"]
        )
        print(f"{i}. {case['tool'].name}: reward={reward:.2f} (success={case['success']}, relevance={case['relevance']}, time={case['time']}s)")
    
    # Test code generation agent reward calculation
    code_agent = CodeGenerationAgent(enable_rl=False)
    
    print("\n--- Code Generation Agent Reward Tests ---")
    from agents.code_generation_agent import CodeQuality
    
    strategy_cases = [
        {"strategy": CodeGenerationStrategy.TEMPLATE_BASED, "success": True, "quality": CodeQuality.GOOD, "time": 0.8},
        {"strategy": CodeGenerationStrategy.LLM_ASSISTED, "success": True, "quality": CodeQuality.EXCELLENT, "time": 2.5},
        {"strategy": CodeGenerationStrategy.HYBRID_APPROACH, "success": False, "quality": CodeQuality.POOR, "time": 4.0},
    ]
    
    for i, case in enumerate(strategy_cases, 1):
        reward = code_agent._calculate_strategy_reward(
            strategy=case["strategy"],
            generation_success=case["success"],
            code_quality=case["quality"],
            generation_time=case["time"]
        )
        print(f"{i}. {case['strategy'].name}: reward={reward:.2f} (success={case['success']}, quality={case['quality'].value}, time={case['time']}s)")


def main():
    """Run all RL integration demos."""
    print("🚀 Task 3.3: Ray RLlib Integration Demo")
    print("=" * 50)
    
    try:
        # Demo 1: Knowledge Agent RL
        demo_knowledge_agent_rl()
        
        # Demo 2: Code Generation Agent RL
        demo_code_generation_agent_rl()
        
        # Demo 3: RL Environment
        demo_rl_environment()
        
        # Demo 4: Reward Function Validation
        demo_reward_function_validation()
        
        print("\n" + "=" * 50)
        print("✅ All RL integration demos completed successfully!")
        print("\nKey achievements:")
        print("- ✅ Ray RLlib integrated in KnowledgeAgent and CodeGenerationAgent")
        print("- ✅ Reward functions correctly calculate and report values")
        print("- ✅ RL environment simulates Agent-Blender interactions")
        print("- ✅ Tool/strategy selection mechanisms working")
        print("- ✅ Performance tracking and optimization enabled")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
