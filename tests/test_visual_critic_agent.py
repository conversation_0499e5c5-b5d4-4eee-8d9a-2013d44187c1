"""
Unit tests for Visual Critic Agent

Tests the visual criticism functionality including multi-modal LLM comparison,
visual consistency evaluation, feedback generation, and outer loop integration.

Author: Augment Agent
Date: 2025-07-18
"""

import os
import json
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from PIL import Image, ImageDraw

from agents.visual_critic_agent import (
    VisualCriticAgent,
    VisualCriticConfig,
    VisualCriticResult,
    VisualEvaluation,
    FeedbackSuggestion,
    VisualDimension,
    ConsistencyLevel,
    FeedbackPriority,
    VisualCriticError
)


class TestVisualCriticAgent:
    """Test cases for Visual Critic Agent."""
    
    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return VisualCriticConfig(
            openai_api_key="test-key",
            openai_model="gpt-4o",
            max_retries=2,
            retry_delay=0.1,
            evaluation_dimensions=[
                VisualDimension.SHAPE,
                VisualDimension.COLOR,
                VisualDimension.POSITION
            ]
        )
    
    @pytest.fixture
    def agent(self, config):
        """Create test agent."""
        with patch('agents.visual_critic_agent.OpenAI'):
            return VisualCriticAgent(config)
    
    @pytest.fixture
    def test_images(self):
        """Create test images."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Create original image (red square)
            original_img = Image.new('RGB', (100, 100), color='red')
            original_path = temp_path / "original.png"
            original_img.save(original_path)
            
            # Create rendered image (blue circle)
            rendered_img = Image.new('RGB', (100, 100), color='white')
            draw = ImageDraw.Draw(rendered_img)
            draw.ellipse([20, 20, 80, 80], fill='blue')
            rendered_path = temp_path / "rendered.png"
            rendered_img.save(rendered_path)
            
            # Create similar image (red square, slightly different)
            similar_img = Image.new('RGB', (100, 100), color='darkred')
            similar_path = temp_path / "similar.png"
            similar_img.save(similar_path)
            
            yield {
                'original': original_path,
                'rendered': rendered_path,
                'similar': similar_path
            }
    
    def test_agent_initialization(self, config):
        """Test agent initialization."""
        with patch('agents.visual_critic_agent.OpenAI') as mock_openai:
            agent = VisualCriticAgent(config)
            
            assert agent.config == config
            mock_openai.assert_called_once_with(api_key="test-key")
    
    def test_agent_initialization_no_api_key(self):
        """Test agent initialization without API key."""
        config = VisualCriticConfig(openai_api_key=None)
        
        with patch.dict(os.environ, {}, clear=True):
            agent = VisualCriticAgent(config)
            assert agent.openai_client is None
    
    def test_encode_image_to_base64(self, agent, test_images):
        """Test image encoding to base64."""
        result = agent._encode_image_to_base64(test_images['original'])
        
        assert isinstance(result, str)
        assert len(result) > 0
        # Should be valid base64
        import base64
        try:
            base64.b64decode(result)
        except Exception:
            pytest.fail("Invalid base64 encoding")
    
    def test_encode_image_nonexistent_file(self, agent):
        """Test encoding nonexistent image file."""
        with pytest.raises(VisualCriticError, match="Failed to encode image"):
            agent._encode_image_to_base64(Path("nonexistent.png"))
    
    def test_create_evaluation_prompt(self, agent):
        """Test evaluation prompt creation."""
        context = {"model_type": "cube", "color": "red"}
        prompt = agent._create_evaluation_prompt(context)
        
        assert "shape, color, position" in prompt.lower()
        assert "json format" in prompt.lower()
        assert "model_type" in prompt
        assert "cube" in prompt
    
    def test_create_evaluation_prompt_no_context(self, agent):
        """Test evaluation prompt creation without context."""
        prompt = agent._create_evaluation_prompt(None)
        
        assert "shape, color, position" in prompt.lower()
        assert "json format" in prompt.lower()
        assert "Context Information" not in prompt
    
    @patch('agents.visual_critic_agent.OpenAI')
    def test_call_multimodal_llm_success(self, mock_openai_class, agent):
        """Test successful LLM call."""
        # Setup mock
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        agent.openai_client = mock_client
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "test response"
        mock_client.chat.completions.create.return_value = mock_response
        
        messages = [{"role": "user", "content": "test"}]
        result = agent._call_multimodal_llm_with_retry(messages)
        
        assert result == "test response"
        mock_client.chat.completions.create.assert_called_once()
    
    @patch('agents.visual_critic_agent.OpenAI')
    def test_call_multimodal_llm_retry(self, mock_openai_class, agent):
        """Test LLM call with retry on failure."""
        # Setup mock
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        agent.openai_client = mock_client
        
        # First call fails, second succeeds
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "success"
        
        mock_client.chat.completions.create.side_effect = [
            Exception("API error"),
            mock_response
        ]
        
        messages = [{"role": "user", "content": "test"}]
        result = agent._call_multimodal_llm_with_retry(messages)
        
        assert result == "success"
        assert mock_client.chat.completions.create.call_count == 2
    
    @patch('agents.visual_critic_agent.OpenAI')
    def test_call_multimodal_llm_all_retries_fail(self, mock_openai_class, agent):
        """Test LLM call when all retries fail."""
        # Setup mock
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        agent.openai_client = mock_client
        
        mock_client.chat.completions.create.side_effect = Exception("API error")
        
        messages = [{"role": "user", "content": "test"}]
        
        with pytest.raises(VisualCriticError, match="All LLM call attempts failed"):
            agent._call_multimodal_llm_with_retry(messages)
    
    def test_parse_evaluation_response_valid_json(self, agent):
        """Test parsing valid LLM response."""
        response = '''
        Here is the evaluation:
        {
            "overall_consistency_score": 0.75,
            "overall_consistency_level": "good",
            "comparison_confidence": 0.8,
            "dimension_evaluations": [
                {
                    "dimension": "shape",
                    "consistency_level": "fair",
                    "score": 0.6,
                    "description": "Shape differs significantly",
                    "specific_issues": ["Different geometry"],
                    "confidence": 0.7
                }
            ],
            "feedback_suggestions": [
                {
                    "priority": "high",
                    "dimension": "shape",
                    "issue_description": "Shape mismatch",
                    "suggested_correction": "Adjust geometry",
                    "actionable_steps": ["Modify shape parameters"],
                    "specificity_score": 4.0,
                    "operability_score": 3.5
                }
            ]
        }
        '''
        
        result = agent._parse_evaluation_response(response, "orig.png", "rend.png")
        
        assert isinstance(result, VisualCriticResult)
        assert result.overall_consistency_score == 0.75
        assert result.overall_consistency_level == ConsistencyLevel.GOOD
        assert len(result.dimension_evaluations) == 1
        assert len(result.feedback_suggestions) == 1
        
        eval = result.dimension_evaluations[0]
        assert eval.dimension == VisualDimension.SHAPE
        assert eval.score == 0.6
        
        feedback = result.feedback_suggestions[0]
        assert feedback.priority == FeedbackPriority.HIGH
        assert feedback.dimension == VisualDimension.SHAPE
    
    def test_parse_evaluation_response_invalid_json(self, agent):
        """Test parsing invalid LLM response."""
        response = "This is not valid JSON response"
        
        result = agent._parse_evaluation_response(response, "orig.png", "rend.png")
        
        # Should return fallback result
        assert isinstance(result, VisualCriticResult)
        assert result.overall_consistency_score == 0.5
        assert result.overall_consistency_level == ConsistencyLevel.FAIR
        assert result.metadata["evaluation_method"] == "fallback_error"
    
    def test_calculate_size_similarity(self, agent):
        """Test size similarity calculation."""
        img1 = Image.new('RGB', (100, 100))
        img2 = Image.new('RGB', (100, 100))
        img3 = Image.new('RGB', (50, 100))
        
        # Same size
        similarity1 = agent._calculate_size_similarity(img1, img2)
        assert similarity1 == 1.0
        
        # Different size
        similarity2 = agent._calculate_size_similarity(img1, img3)
        assert 0.5 < similarity2 < 1.0
    
    def test_calculate_basic_color_similarity(self, agent):
        """Test basic color similarity calculation."""
        img1 = Image.new('RGB', (10, 10), color='red')
        img2 = Image.new('RGB', (10, 10), color='red')
        img3 = Image.new('RGB', (10, 10), color='blue')
        
        # Same color
        similarity1 = agent._calculate_basic_color_similarity(img1, img2)
        assert similarity1 > 0.9
        
        # Different color
        similarity2 = agent._calculate_basic_color_similarity(img1, img3)
        assert similarity2 < 0.5
    
    def test_score_to_consistency_level(self, agent):
        """Test score to consistency level conversion."""
        assert agent._score_to_consistency_level(0.95) == ConsistencyLevel.EXCELLENT
        assert agent._score_to_consistency_level(0.8) == ConsistencyLevel.GOOD
        assert agent._score_to_consistency_level(0.6) == ConsistencyLevel.FAIR
        assert agent._score_to_consistency_level(0.3) == ConsistencyLevel.POOR
        assert agent._score_to_consistency_level(0.1) == ConsistencyLevel.VERY_POOR

    def test_evaluate_with_fallback_method(self, agent, test_images):
        """Test fallback evaluation method."""
        result = agent._evaluate_with_fallback_method(
            test_images['original'],
            test_images['similar'],
            None
        )

        assert isinstance(result, VisualCriticResult)
        assert 0.0 <= result.overall_consistency_score <= 1.0
        assert result.metadata["evaluation_method"] == "fallback_basic"
        assert len(result.dimension_evaluations) >= 2  # Shape and color
        assert len(result.feedback_suggestions) >= 1

    def test_evaluate_visual_consistency_no_llm(self, test_images):
        """Test visual consistency evaluation without LLM."""
        config = VisualCriticConfig(openai_api_key=None)
        agent = VisualCriticAgent(config)

        result = agent.evaluate_visual_consistency(
            test_images['original'],
            test_images['rendered']
        )

        assert isinstance(result, VisualCriticResult)
        assert result.processing_time > 0
        assert result.metadata["evaluation_method"] == "fallback_basic"

    def test_evaluate_visual_consistency_nonexistent_files(self, agent):
        """Test evaluation with nonexistent files."""
        with pytest.raises(VisualCriticError, match="Original image not found"):
            agent.evaluate_visual_consistency("nonexistent1.png", "nonexistent2.png")

    @patch('agents.visual_critic_agent.OpenAI')
    def test_evaluate_visual_consistency_with_llm(self, mock_openai_class, test_images):
        """Test visual consistency evaluation with LLM."""
        # Setup mock
        mock_client = Mock()
        mock_openai_class.return_value = mock_client

        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '''
        {
            "overall_consistency_score": 0.7,
            "overall_consistency_level": "good",
            "comparison_confidence": 0.8,
            "dimension_evaluations": [
                {
                    "dimension": "shape",
                    "consistency_level": "fair",
                    "score": 0.6,
                    "description": "Different shapes detected",
                    "specific_issues": ["Square vs circle"],
                    "confidence": 0.8
                }
            ],
            "feedback_suggestions": [
                {
                    "priority": "high",
                    "dimension": "shape",
                    "issue_description": "Shape mismatch between original and rendered",
                    "suggested_correction": "Adjust model geometry to match original",
                    "actionable_steps": ["Change primitive type", "Adjust dimensions"],
                    "specificity_score": 4.0,
                    "operability_score": 4.5
                }
            ]
        }
        '''
        mock_client.chat.completions.create.return_value = mock_response

        config = VisualCriticConfig(openai_api_key="test-key")
        agent = VisualCriticAgent(config)
        agent.openai_client = mock_client

        result = agent.evaluate_visual_consistency(
            test_images['original'],
            test_images['rendered'],
            context={"model_type": "cube"}
        )

        assert isinstance(result, VisualCriticResult)
        assert result.overall_consistency_score == 0.7
        assert result.overall_consistency_level == ConsistencyLevel.GOOD
        assert result.processing_time > 0
        assert len(result.dimension_evaluations) == 1
        assert len(result.feedback_suggestions) == 1

        # Verify LLM was called
        mock_client.chat.completions.create.assert_called_once()
        call_args = mock_client.chat.completions.create.call_args
        assert call_args[1]['model'] == "gpt-4o"
        assert len(call_args[1]['messages'][0]['content']) == 4  # Text + 2 images + text

    def test_generate_feedback_for_spec_agent(self, agent):
        """Test feedback generation for SpecGenerationAgent."""
        # Create test critic result
        critic_result = VisualCriticResult(
            overall_consistency_score=0.6,
            overall_consistency_level=ConsistencyLevel.FAIR,
            dimension_evaluations=[
                VisualEvaluation(
                    dimension=VisualDimension.SHAPE,
                    consistency_level=ConsistencyLevel.POOR,
                    score=0.4,
                    description="Shape mismatch",
                    specific_issues=["Wrong geometry"],
                    confidence=0.8
                )
            ],
            feedback_suggestions=[
                FeedbackSuggestion(
                    priority=FeedbackPriority.CRITICAL,
                    dimension=VisualDimension.SHAPE,
                    issue_description="Major shape difference",
                    suggested_correction="Change to sphere",
                    actionable_steps=["Update geometry type"],
                    specificity_score=4.5,
                    operability_score=4.0
                ),
                FeedbackSuggestion(
                    priority=FeedbackPriority.HIGH,
                    dimension=VisualDimension.COLOR,
                    issue_description="Color mismatch",
                    suggested_correction="Adjust material color",
                    actionable_steps=["Update color values"],
                    specificity_score=3.5,
                    operability_score=4.5
                )
            ],
            processing_time=1.5,
            comparison_confidence=0.8,
            metadata={"test": "data"}
        )

        original_spec = {"geometry": "cube", "color": "red"}

        feedback = agent.generate_feedback_for_spec_agent(critic_result, original_spec)

        assert feedback["feedback_type"] == "visual_critic"
        assert "timestamp" in feedback
        assert feedback["overall_assessment"]["consistency_score"] == 0.6
        assert feedback["overall_assessment"]["consistency_level"] == "fair"
        assert feedback["requires_regeneration"] is True  # Score < 0.7

        # Check critical issues
        assert len(feedback["critical_issues"]) == 1
        critical = feedback["critical_issues"][0]
        assert critical["dimension"] == "shape"
        assert critical["issue"] == "Major shape difference"

        # Check improvement suggestions
        assert len(feedback["improvement_suggestions"]) == 1
        improvement = feedback["improvement_suggestions"][0]
        assert improvement["dimension"] == "color"
        assert improvement["priority"] == "high"

        # Check dimension scores
        assert "shape" in feedback["dimension_scores"]
        assert feedback["dimension_scores"]["shape"]["score"] == 0.4

        # Check original spec context
        assert feedback["original_spec_context"] == original_spec

    def test_generate_feedback_for_spec_agent_no_regeneration(self, agent):
        """Test feedback generation when no regeneration is needed."""
        critic_result = VisualCriticResult(
            overall_consistency_score=0.8,  # High score
            overall_consistency_level=ConsistencyLevel.GOOD,
            dimension_evaluations=[],
            feedback_suggestions=[],
            processing_time=1.0,
            comparison_confidence=0.9,
            metadata={}
        )

        feedback = agent.generate_feedback_for_spec_agent(critic_result)

        assert feedback["requires_regeneration"] is False  # Score >= 0.7
        assert feedback["overall_assessment"]["consistency_score"] == 0.8

    def test_get_evaluation_summary(self, agent):
        """Test evaluation summary generation."""
        critic_result = VisualCriticResult(
            overall_consistency_score=0.75,
            overall_consistency_level=ConsistencyLevel.GOOD,
            dimension_evaluations=[
                VisualEvaluation(
                    dimension=VisualDimension.SHAPE,
                    consistency_level=ConsistencyLevel.FAIR,
                    score=0.6,
                    description="Shape differs",
                    specific_issues=["Different geometry"],
                    confidence=0.8
                ),
                VisualEvaluation(
                    dimension=VisualDimension.COLOR,
                    consistency_level=ConsistencyLevel.GOOD,
                    score=0.8,
                    description="Color matches well",
                    specific_issues=[],
                    confidence=0.9
                )
            ],
            feedback_suggestions=[
                FeedbackSuggestion(
                    priority=FeedbackPriority.HIGH,
                    dimension=VisualDimension.SHAPE,
                    issue_description="Shape needs adjustment",
                    suggested_correction="Modify geometry",
                    actionable_steps=["Change shape type"],
                    specificity_score=4.0,
                    operability_score=3.5
                )
            ],
            processing_time=2.1,
            comparison_confidence=0.85,
            metadata={}
        )

        summary = agent.get_evaluation_summary(critic_result)

        assert "Visual Consistency Evaluation Summary" in summary
        assert "Overall Score: 0.75/1.0" in summary
        assert "Overall Level: Good" in summary
        assert "Confidence: 0.85" in summary
        assert "Processing Time: 2.10s" in summary
        assert "Shape: 0.60 (fair)" in summary
        assert "Color: 0.80 (good)" in summary
        assert "[HIGH] Shape: Shape needs adjustment" in summary

    def test_visual_critic_error_handling(self, agent):
        """Test error handling in various scenarios."""
        # Test with invalid image path
        with pytest.raises(VisualCriticError):
            agent.evaluate_visual_consistency("invalid.png", "also_invalid.png")

        # Test feedback generation with invalid input
        with pytest.raises(VisualCriticError):
            agent.generate_feedback_for_spec_agent(None)


class TestVisualCriticIntegration:
    """Integration tests for Visual Critic Agent."""

    @pytest.fixture
    def temp_images(self):
        """Create temporary test images for integration tests."""
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create more realistic test images
            original_img = Image.new('RGB', (256, 256), color='white')
            draw = ImageDraw.Draw(original_img)
            draw.rectangle([64, 64, 192, 192], fill='red', outline='black')
            original_path = temp_path / "original.png"
            original_img.save(original_path)

            # Create rendered image with slight differences
            rendered_img = Image.new('RGB', (256, 256), color='lightgray')
            draw = ImageDraw.Draw(rendered_img)
            draw.rectangle([70, 70, 186, 186], fill='darkred', outline='black')
            rendered_path = temp_path / "rendered.png"
            rendered_img.save(rendered_path)

            yield {
                'original': original_path,
                'rendered': rendered_path
            }

    def test_end_to_end_evaluation_fallback(self, temp_images):
        """Test end-to-end evaluation using fallback method."""
        config = VisualCriticConfig(
            openai_api_key=None,  # Force fallback
            evaluation_dimensions=[
                VisualDimension.SHAPE,
                VisualDimension.COLOR,
                VisualDimension.POSITION
            ]
        )
        agent = VisualCriticAgent(config)

        result = agent.evaluate_visual_consistency(
            temp_images['original'],
            temp_images['rendered'],
            context={"model_type": "cube", "color": "red"}
        )

        # Verify result structure
        assert isinstance(result, VisualCriticResult)
        assert 0.0 <= result.overall_consistency_score <= 1.0
        assert result.processing_time > 0
        assert len(result.dimension_evaluations) >= 2
        assert len(result.feedback_suggestions) >= 1

        # Generate feedback for spec agent
        feedback = agent.generate_feedback_for_spec_agent(result)
        assert feedback["feedback_type"] == "visual_critic"
        assert "overall_assessment" in feedback
        assert "dimension_scores" in feedback

        # Generate summary
        summary = agent.get_evaluation_summary(result)
        assert "Visual Consistency Evaluation Summary" in summary
        assert f"{result.overall_consistency_score:.2f}" in summary

    def test_outer_loop_integration_simulation(self, temp_images):
        """Test simulation of outer loop integration with SpecGenerationAgent."""
        agent = VisualCriticAgent(VisualCriticConfig(openai_api_key=None))

        # Simulate original model specification
        original_spec = {
            "geometry": {
                "type": "cube",
                "dimensions": [2, 2, 2]
            },
            "material": {
                "color": [1.0, 0.0, 0.0],  # Red
                "type": "basic"
            },
            "transform": {
                "position": [0, 0, 0],
                "rotation": [0, 0, 0],
                "scale": [1, 1, 1]
            }
        }

        # Evaluate visual consistency
        result = agent.evaluate_visual_consistency(
            temp_images['original'],
            temp_images['rendered'],
            context={"original_spec": original_spec}
        )

        # Generate feedback for SpecGenerationAgent
        feedback = agent.generate_feedback_for_spec_agent(result, original_spec)

        # Verify feedback structure for outer loop
        assert feedback["feedback_type"] == "visual_critic"
        assert "requires_regeneration" in feedback
        assert "critical_issues" in feedback
        assert "improvement_suggestions" in feedback
        assert "dimension_scores" in feedback
        assert "original_spec_context" in feedback

        # Simulate SpecGenerationAgent response to feedback
        if feedback["requires_regeneration"]:
            # Would trigger spec regeneration in real system
            assert len(feedback["critical_issues"]) > 0 or len(feedback["improvement_suggestions"]) > 0

        # Verify actionable feedback
        for suggestion in feedback["improvement_suggestions"]:
            assert "dimension" in suggestion
            assert "correction" in suggestion
            assert suggestion["specificity"] > 0
            assert suggestion["operability"] > 0
