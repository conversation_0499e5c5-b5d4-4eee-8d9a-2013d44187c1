"""
Test suite for Task 3.3: Ray RLlib基础集成、奖励函数设计与RL环境构建

This module contains comprehensive tests for RL integration in KnowledgeAgent
and CodeGenerationAgent, reward function validation, and RL environment testing.
"""

import unittest
import sys
import os
import json
import time
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from agents.knowledge_agent import (
    KnowledgeAgent, KnowledgeToolType, ToolSelectionContext
)
from agents.code_generation_agent import (
    CodeGenerationAgent, CodeGenerationStrategy, CodeGenerationContext, CodeQuality
)
from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv, TaskType, ToolType


class TestKnowledgeAgentRLIntegration(unittest.TestCase):
    """Test RL integration in KnowledgeAgent."""
    
    def setUp(self):
        """Set up test environment."""
        # Create agent with R<PERSON> disabled for testing
        self.agent = KnowledgeAgent(enable_rl=False)
        
        # Mock RL components for testing
        self.agent.enable_rl = True
        self.agent.rl_policy = MagicMock()
        self.agent.rl_policy.compute_single_action.return_value = 1
    
    def test_tool_selection_context_creation(self):
        """Test creation of tool selection context."""
        context = ToolSelectionContext(
            query_type="blender api",
            query_complexity=0.7,
            domain_specificity=0.9,
            previous_tool_success=0.8,
            time_constraint=0.5
        )
        
        self.assertEqual(context.query_type, "blender api")
        self.assertEqual(context.query_complexity, 0.7)
        self.assertEqual(context.domain_specificity, 0.9)
    
    def test_rl_tool_selection(self):
        """Test RL-based tool selection."""
        context = ToolSelectionContext(
            query_type="blender python",
            query_complexity=0.6,
            domain_specificity=0.8,
            previous_tool_success=0.7,
            time_constraint=0.4
        )
        
        selected_tool = self.agent._select_optimal_tool(context)
        self.assertIsInstance(selected_tool, KnowledgeToolType)
    
    def test_heuristic_tool_selection(self):
        """Test fallback heuristic tool selection."""
        # Test Blender API query
        context = ToolSelectionContext(
            query_type="blender api mesh",
            query_complexity=0.5,
            domain_specificity=0.8,
            previous_tool_success=0.7,
            time_constraint=0.5
        )
        
        tool = self.agent._heuristic_tool_selection(context)
        self.assertEqual(tool, KnowledgeToolType.BLENDER_API_LOOKUP)
        
        # Test MCP query
        context.query_type = "molecular nodes example"
        tool = self.agent._heuristic_tool_selection(context)
        self.assertEqual(tool, KnowledgeToolType.MCP_EXAMPLES)
    
    def test_reward_calculation(self):
        """Test reward calculation for tool performance."""
        tool = KnowledgeToolType.VECTOR_SEARCH
        
        # Test successful query
        reward = self.agent._calculate_tool_reward(
            tool=tool,
            query_success=True,
            relevance_score=0.9,
            response_time=1.5
        )
        self.assertGreater(reward, 0)
        
        # Test failed query
        reward = self.agent._calculate_tool_reward(
            tool=tool,
            query_success=False,
            relevance_score=0.2,
            response_time=3.0
        )
        self.assertLess(reward, 0)
    
    def test_performance_history_tracking(self):
        """Test tool performance history tracking."""
        tool = KnowledgeToolType.BLENDER_API_LOOKUP
        initial_count = len(self.agent.tool_performance_history[tool])
        
        # Add some performance data
        self.agent._calculate_tool_reward(tool, True, 0.8, 1.0)
        self.agent._calculate_tool_reward(tool, False, 0.3, 2.0)
        
        final_count = len(self.agent.tool_performance_history[tool])
        self.assertEqual(final_count, initial_count + 2)


class TestCodeGenerationAgentRLIntegration(unittest.TestCase):
    """Test RL integration in CodeGenerationAgent."""
    
    def setUp(self):
        """Set up test environment."""
        # Create agent with RL disabled for testing
        self.agent = CodeGenerationAgent(enable_rl=False)
        
        # Mock RL components
        self.agent.enable_rl = True
        self.agent.rl_policy = MagicMock()
        self.agent.rl_policy.compute_single_action.return_value = 2
    
    def test_generation_context_creation(self):
        """Test creation of code generation context."""
        context = CodeGenerationContext(
            spec_complexity=0.7,
            geometry_count=3,
            has_materials=True,
            has_animations=False,
            knowledge_availability=0.8,
            time_constraint=0.6,
            quality_requirement=0.9
        )
        
        self.assertEqual(context.spec_complexity, 0.7)
        self.assertEqual(context.geometry_count, 3)
        self.assertTrue(context.has_materials)
        self.assertFalse(context.has_animations)
    
    def test_rl_strategy_selection(self):
        """Test RL-based strategy selection."""
        context = CodeGenerationContext(
            spec_complexity=0.6,
            geometry_count=2,
            has_materials=False,
            has_animations=False,
            knowledge_availability=0.7,
            time_constraint=0.5,
            quality_requirement=0.8
        )
        
        strategy = self.agent._select_generation_strategy(context)
        self.assertIsInstance(strategy, CodeGenerationStrategy)
    
    def test_heuristic_strategy_selection(self):
        """Test fallback heuristic strategy selection."""
        # Test high complexity with knowledge
        context = CodeGenerationContext(
            spec_complexity=0.9,
            geometry_count=2,
            has_materials=True,
            has_animations=False,
            knowledge_availability=0.8,
            time_constraint=0.5,
            quality_requirement=0.8
        )
        
        strategy = self.agent._heuristic_strategy_selection(context)
        self.assertEqual(strategy, CodeGenerationStrategy.KNOWLEDGE_GUIDED)
        
        # Test time constraint
        context.time_constraint = 0.8
        context.spec_complexity = 0.3
        strategy = self.agent._heuristic_strategy_selection(context)
        self.assertEqual(strategy, CodeGenerationStrategy.TEMPLATE_BASED)
    
    def test_strategy_reward_calculation(self):
        """Test reward calculation for strategy performance."""
        strategy = CodeGenerationStrategy.LLM_ASSISTED
        
        # Test successful generation
        reward = self.agent._calculate_strategy_reward(
            strategy=strategy,
            generation_success=True,
            code_quality=CodeQuality.GOOD,
            generation_time=2.0,
            execution_success=True
        )
        self.assertGreater(reward, 0)
        
        # Test failed generation
        reward = self.agent._calculate_strategy_reward(
            strategy=strategy,
            generation_success=False,
            code_quality=CodeQuality.INVALID,
            generation_time=5.0,
            execution_success=False
        )
        self.assertLess(reward, 0)
    
    def test_spec_complexity_calculation(self):
        """Test specification complexity calculation."""
        # Simple spec
        simple_spec = {
            'objects': [
                {'geometry': {'type': 'cube'}}
            ]
        }
        complexity = self.agent._calculate_spec_complexity(simple_spec)
        self.assertLess(complexity, 0.5)
        
        # Complex spec
        complex_spec = {
            'objects': [
                {
                    'geometry': {'type': 'mesh'},
                    'material': {'type': 'pbr'},
                    'animation': {'type': 'rotation'}
                },
                {'geometry': {'type': 'cube'}},
                {'geometry': {'type': 'sphere'}}
            ]
        }
        complexity = self.agent._calculate_spec_complexity(complex_spec)
        self.assertGreater(complexity, 0.5)


class TestRLEnvironmentIntegration(unittest.TestCase):
    """Test RL environment integration and reward functions."""
    
    def setUp(self):
        """Set up test environment."""
        self.env = MinimalBlenderTaskEnv()
    
    def test_environment_reward_calculation(self):
        """Test reward calculation in RL environment."""
        self.env.reset()
        
        # Test appropriate tool selection
        self.env.current_task = TaskType.IMAGE_ANALYSIS
        observation, reward, terminated, truncated, info = self.env.step(ToolType.ANALYZE_IMAGE.value)
        
        self.assertGreater(reward, 0)  # Should get positive reward
        self.assertEqual(info['selected_tool'], 'ANALYZE_IMAGE')
    
    def test_reward_function_consistency(self):
        """Test consistency of reward function across multiple episodes."""
        rewards = []
        
        for episode in range(5):
            self.env.reset()
            self.env.current_task = TaskType.CODE_GENERATION
            
            # Use appropriate tool
            _, reward, _, _, _ = self.env.step(ToolType.GENERATE_CODE.value)
            rewards.append(reward)
        
        # Rewards should be consistent for same action-state pairs
        self.assertTrue(all(r > 0 for r in rewards))  # All should be positive
        
        # Variance should be reasonable
        import statistics
        if len(rewards) > 1:
            variance = statistics.variance(rewards)
            self.assertLess(variance, 10.0)  # Reasonable variance
    
    def test_task_completion_detection(self):
        """Test task completion detection in RL environment."""
        self.env.reset()
        
        # Simulate successful task completion
        self.env.task_progress = 8.5
        self.assertTrue(self.env._is_task_complete())
        
        # Test incomplete task
        self.env.task_progress = 5.0
        self.assertFalse(self.env._is_task_complete())


class TestEndToEndRLIntegration(unittest.TestCase):
    """Test end-to-end RL integration scenarios."""
    
    def test_knowledge_agent_rl_query(self):
        """Test RL-enhanced knowledge query."""
        agent = KnowledgeAgent(enable_rl=False)  # Disable for testing
        
        # Mock successful query
        with patch.object(agent, '_execute_tool_query') as mock_execute:
            mock_execute.return_value = []
            
            results = agent.query_knowledge(
                query="blender python create cube",
                enable_rl_selection=False
            )
            
            self.assertIsInstance(results, list)
            mock_execute.assert_called_once()
    
    def test_code_generation_rl_strategy(self):
        """Test RL-enhanced code generation."""
        agent = CodeGenerationAgent(enable_rl=False)
        
        # Test specification
        spec = {
            'schema_version': '1.0',
            'model_info': {'name': 'test'},
            'objects': [
                {'geometry': {'type': 'cube'}}
            ]
        }
        
        # Mock code generation
        with patch.object(agent, '_generate_code_from_template') as mock_generate:
            mock_generate.return_value = "# Test code"
            
            with patch.object(agent, '_analyze_code') as mock_analyze:
                from agents.code_generation_agent import CodeAnalysisResult
                mock_analyze.return_value = CodeAnalysisResult(
                    syntax_valid=True,
                    ast_parseable=True,
                    quality_score=0.8,
                    quality_level=CodeQuality.GOOD,
                    issues=[],
                    suggestions=[],
                    complexity_score=5,
                    line_count=20
                )
                
                result = agent.generate_blender_code(spec, enable_rl_strategy=False)
                
                self.assertIsNotNone(result)
                self.assertEqual(result.generated_code, "# Test code")


def run_rl_integration_tests():
    """Run all RL integration tests and return results."""
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestKnowledgeAgentRLIntegration,
        TestCodeGenerationAgentRLIntegration,
        TestRLEnvironmentIntegration,
        TestEndToEndRLIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result


if __name__ == "__main__":
    print("=== Running Task 3.3 RL Integration Tests ===\n")
    
    result = run_rl_integration_tests()
    
    print(f"\n=== Test Results ===")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\nSuccess rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("✅ RL integration tests passed successfully!")
    else:
        print("❌ Some RL integration tests failed. Please review and fix issues.")
