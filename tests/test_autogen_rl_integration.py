"""
Test suite for AutoGen + Ray RLlib integration (Task 1.2)

This module contains comprehensive tests for the agent framework integration,
communication protocol compliance, and RL environment functionality.
"""

import unittest
import sys
import os
import json
import numpy as np
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from agents.autogen_om_poc import (
    AgentCommunicationProtocol, 
    SimpleToolSelectionEnv, 
    RLEnhancedAgent,
    RLEnhancedAssistantAgent
)
from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv, TaskType, ToolType

class TestAgentCommunicationProtocol(unittest.TestCase):
    """Test the agent communication protocol implementation."""
    
    def setUp(self):
        self.protocol = AgentCommunicationProtocol()
    
    def test_create_message(self):
        """Test message creation according to protocol v1."""
        message = self.protocol.create_message(
            sender_id="test_agent",
            receiver_id="target_agent", 
            message_type="test_message",
            payload={"data": "test"},
            metadata={"confidence": 0.95}
        )
        
        # Check required fields
        required_fields = ["message_id", "sender_id", "receiver_id", 
                          "message_type", "timestamp", "payload", "metadata"]
        for field in required_fields:
            self.assertIn(field, message)
        
        # Check field values
        self.assertEqual(message["sender_id"], "test_agent")
        self.assertEqual(message["receiver_id"], "target_agent")
        self.assertEqual(message["message_type"], "test_message")
        self.assertEqual(message["payload"]["data"], "test")
        self.assertEqual(message["metadata"]["confidence"], 0.95)
    
    def test_validate_message(self):
        """Test message validation."""
        # Valid message
        valid_message = {
            "message_id": "test-123",
            "sender_id": "agent1",
            "receiver_id": "agent2",
            "message_type": "test",
            "timestamp": "2024-01-01T00:00:00",
            "payload": {},
            "metadata": {}
        }
        self.assertTrue(self.protocol.validate_message(valid_message))
        
        # Invalid message (missing field)
        invalid_message = valid_message.copy()
        del invalid_message["payload"]
        self.assertFalse(self.protocol.validate_message(invalid_message))

class TestSimpleToolSelectionEnv(unittest.TestCase):
    """Test the simple RL environment for tool selection."""
    
    def setUp(self):
        self.env = SimpleToolSelectionEnv()
    
    def test_environment_initialization(self):
        """Test environment initialization."""
        self.assertEqual(len(self.env.available_tools), 4)
        self.assertEqual(self.env.observation_space.shape, (4,))
        self.assertEqual(self.env.action_space.n, 4)
    
    def test_reset(self):
        """Test environment reset functionality."""
        observation, info = self.env.reset()
        
        self.assertEqual(len(observation), 4)
        self.assertEqual(self.env.step_count, 0)
        self.assertIsInstance(info, dict)
    
    def test_step(self):
        """Test environment step functionality."""
        self.env.reset()
        
        # Take a step
        action = 1  # analyze_image
        observation, reward, done, truncated, info = self.env.step(action)
        
        self.assertEqual(len(observation), 4)
        self.assertIsInstance(reward, (int, float))
        self.assertIsInstance(done, bool)
        self.assertIsInstance(truncated, bool)
        self.assertIn("selected_tool", info)
        self.assertEqual(info["selected_tool"], "analyze_image")
    
    def test_reward_calculation(self):
        """Test reward calculation logic."""
        self.env.reset()
        
        # Test different actions
        rewards = []
        for action in range(self.env.action_space.n):
            self.env.reset()
            _, reward, _, _, _ = self.env.step(action)
            rewards.append(reward)
        
        # Should have different rewards for different actions
        self.assertTrue(len(set(rewards)) > 1)

class TestMinimalBlenderTaskEnv(unittest.TestCase):
    """Test the minimal Blender task environment."""
    
    def setUp(self):
        self.env = MinimalBlenderTaskEnv()
    
    def test_environment_initialization(self):
        """Test environment initialization."""
        self.assertEqual(self.env.observation_space.shape, (6,))
        self.assertEqual(self.env.action_space.n, len(ToolType))
        self.assertIsInstance(self.env.current_task, TaskType)
    
    def test_reset(self):
        """Test environment reset."""
        observation, info = self.env.reset()
        
        self.assertEqual(len(observation), 6)
        self.assertEqual(self.env.step_count, 0)
        self.assertEqual(self.env.task_progress, 0.0)
        self.assertEqual(self.env.tools_used_count, 0)
    
    def test_step_execution(self):
        """Test step execution with different tools."""
        self.env.reset()
        
        # Test each tool type
        for tool_idx in range(len(ToolType)):
            self.env.reset()
            observation, reward, terminated, truncated, info = self.env.step(tool_idx)
            
            self.assertEqual(len(observation), 6)
            self.assertIsInstance(reward, (int, float))
            self.assertIn("selected_tool", info)
            self.assertIn("task_type", info)
            self.assertEqual(self.env.tools_used_count, 1)
    
    def test_task_completion(self):
        """Test task completion logic."""
        self.env.reset()
        
        # Simulate successful task completion
        self.env.task_progress = 8.5
        self.assertTrue(self.env._is_task_complete())
        
        # Test incomplete task
        self.env.task_progress = 5.0
        self.assertFalse(self.env._is_task_complete())
    
    def test_tool_appropriateness(self):
        """Test tool appropriateness calculation."""
        self.env.reset()
        self.env.current_task = TaskType.IMAGE_ANALYSIS
        
        # ANALYZE_IMAGE should be highly appropriate for IMAGE_ANALYSIS
        analyze_reward = self.env._calculate_tool_appropriateness(ToolType.ANALYZE_IMAGE)
        hello_reward = self.env._calculate_tool_appropriateness(ToolType.PRINT_HELLO)
        
        self.assertGreater(analyze_reward, hello_reward)

class TestRLEnhancedAgent(unittest.TestCase):
    """Test the RL-enhanced agent functionality."""

    @patch('ray.init')
    @patch('ray.is_initialized')
    def setUp(self, mock_ray_initialized, mock_ray_init):
        """Set up test with mocked Ray initialization."""
        mock_ray_init.return_value = None
        mock_ray_initialized.return_value = False

        # Mock the PPO algorithm to avoid actual training
        with patch('ray.rllib.algorithms.ppo.PPOConfig') as mock_ppo_config:
            mock_config = MagicMock()
            mock_config.environment.return_value = mock_config
            mock_config.framework.return_value = mock_config
            mock_config.training.return_value = mock_config
            mock_config.resources.return_value = mock_config

            mock_algorithm = MagicMock()
            mock_algorithm.train.return_value = {'episode_reward_mean': 5.0}
            mock_algorithm.compute_single_action.return_value = 1
            mock_config.build.return_value = mock_algorithm

            mock_ppo_config.return_value = mock_config

            # Patch HAS_RAY to True for testing
            with patch('agents.autogen_om_poc.HAS_RAY', True):
                self.agent = RLEnhancedAgent("test_agent")
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertEqual(self.agent.agent_id, "test_agent")
        self.assertIsNotNone(self.agent.communication)
        self.assertIsNotNone(self.agent.env)
    
    def test_tool_selection(self):
        """Test RL-driven tool selection."""
        context = {"message_content": "analyze this image"}
        selected_tool = self.agent.select_tool_with_rl(context)
        
        self.assertIn(selected_tool, self.agent.env.available_tools)
    
    def test_message_sending(self):
        """Test message sending with protocol compliance."""
        message = self.agent.send_message(
            receiver_id="target_agent",
            message_type="test_message",
            payload={"data": "test"}
        )
        
        self.assertTrue(self.agent.communication.validate_message(message))
        self.assertEqual(message["sender_id"], "test_agent")
        self.assertEqual(message["receiver_id"], "target_agent")

class TestIntegrationScenarios(unittest.TestCase):
    """Test integration scenarios for the complete system."""
    
    def test_protocol_message_flow(self):
        """Test complete message flow using the communication protocol."""
        protocol = AgentCommunicationProtocol()
        
        # Simulate agent conversation
        messages = []
        
        # Agent 1 sends image analysis request
        msg1 = protocol.create_message(
            sender_id="ImageAnalysisAgent",
            receiver_id="SpecGenerationAgent", 
            message_type="image_analysis_result",
            payload={
                "source_image_path": "/test/image.png",
                "detected_objects": [
                    {"label": "cube", "bbox": [10, 10, 50, 50]}
                ]
            },
            metadata={"confidence_score": 0.95}
        )
        messages.append(msg1)
        
        # Agent 2 responds with spec generation
        msg2 = protocol.create_message(
            sender_id="SpecGenerationAgent",
            receiver_id="CodeGenerationAgent",
            message_type="spec_generation_result", 
            payload={
                "model_spec": {
                    "type": "cube",
                    "size": 2.0,
                    "position": [0, 0, 0]
                }
            }
        )
        messages.append(msg2)
        
        # Validate all messages
        for msg in messages:
            self.assertTrue(protocol.validate_message(msg))
        
        # Check message chain
        self.assertEqual(len(messages), 2)
        self.assertEqual(messages[0]["message_type"], "image_analysis_result")
        self.assertEqual(messages[1]["message_type"], "spec_generation_result")
    
    def test_rl_environment_episode(self):
        """Test complete RL environment episode."""
        env = MinimalBlenderTaskEnv()
        observation, _ = env.reset()
        
        total_reward = 0
        steps = 0
        max_steps = 10
        
        while steps < max_steps:
            # Select random action for testing
            action = env.action_space.sample()
            observation, reward, terminated, truncated, info = env.step(action)
            
            total_reward += reward
            steps += 1
            
            if terminated or truncated:
                break
        
        # Verify episode completed properly
        self.assertGreater(steps, 0)
        self.assertIsInstance(total_reward, (int, float))

def run_integration_tests():
    """Run all integration tests and return results."""
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestAgentCommunicationProtocol,
        TestSimpleToolSelectionEnv, 
        TestMinimalBlenderTaskEnv,
        TestRLEnhancedAgent,
        TestIntegrationScenarios
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result

if __name__ == "__main__":
    print("=== Running AutoGen + Ray RLlib Integration Tests ===\n")
    
    result = run_integration_tests()
    
    print(f"\n=== Test Results ===")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\nSuccess rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("✅ Integration tests passed successfully!")
    else:
        print("❌ Some integration tests failed. Please review and fix issues.")
