"""
Unit tests for Code Generation Agent

This module contains comprehensive tests for the CodeGenerationAgent including:
- Basic code generation from JSON specifications
- Static code analysis and validation
- Error handling scenarios
- Integration with Knowledge Agent
- Code quality assessment and confidence scoring

Author: Augment Agent
Date: 2025-07-18
"""

import unittest
import json
import ast
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from pathlib import Path

# Import the modules to test
from agents.code_generation_agent import (
    CodeGenerationAgent, CodeGenerationConfig, CodeGenerationResult,
    CodeAnalysisResult, CodeQuality, CodeGenerationError
)
from agents.knowledge_agent import KnowledgeAgent, RetrievalResult, KnowledgeChunk, KnowledgeSource


class TestCodeGenerationAgent(unittest.TestCase):
    """Test cases for CodeGenerationAgent."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = CodeGenerationConfig(
            max_retries=1,  # Reduce retries for faster testing
            enable_static_analysis=True
        )
        
        # Create agent without OpenAI client for most tests
        self.agent = CodeGenerationAgent(config=self.config)
        
        # Create sample JSON specification
        self.sample_spec = {
            "schema_version": "v1.0.0",
            "model_info": {
                "name": "Test Model",
                "description": "A test 3D model",
                "created_at": "2025-07-18T10:00:00Z",
                "tags": ["test", "basic"]
            },
            "scene_settings": {
                "units": "meters"
            },
            "objects": [
                {
                    "id": "cube_1",
                    "name": "Test Cube",
                    "geometry": {
                        "type": "cube",
                        "size": 2.0
                    },
                    "transform": {
                        "position": {"x": 0.0, "y": 0.0, "z": 0.0},
                        "rotation": {"x": 0.0, "y": 0.0, "z": 0.0},
                        "scale": {"x": 1.0, "y": 1.0, "z": 1.0}
                    },
                    "material": {
                        "type": "basic",
                        "name": "red_material",
                        "color": {"r": 1.0, "g": 0.0, "b": 0.0, "a": 1.0}
                    },
                    "visible": True
                }
            ]
        }
        
        # Create sample knowledge context
        self.sample_knowledge_chunk = KnowledgeChunk(
            id="test_chunk_1",
            content="bpy.ops.mesh.primitive_cube_add() creates a cube mesh in Blender",
            source=KnowledgeSource.BLENDER_API,
            topic="mesh_creation"
        )
        
        self.sample_knowledge_result = RetrievalResult(
            chunk=self.sample_knowledge_chunk,
            relevance_score=0.9,
            distance=0.1
        )
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        agent = CodeGenerationAgent()
        self.assertIsNotNone(agent)
        self.assertIsNotNone(agent.config)
        self.assertIsNotNone(agent.code_templates)
        self.assertIn('cube', agent.code_templates)
        self.assertIn('sphere', agent.code_templates)
    
    def test_basic_code_generation(self):
        """Test basic code generation from specification."""
        result = self.agent.generate_blender_code(self.sample_spec)
        
        self.assertIsInstance(result, CodeGenerationResult)
        self.assertIsNotNone(result.generated_code)
        self.assertTrue(len(result.generated_code) > 0)
        self.assertEqual(result.specification_used, self.sample_spec)
        self.assertIsInstance(result.analysis_result, CodeAnalysisResult)
        self.assertGreater(result.confidence_score, 0.0)
        self.assertLessEqual(result.confidence_score, 1.0)
    
    def test_code_syntax_validation(self):
        """Test that generated code has valid Python syntax."""
        result = self.agent.generate_blender_code(self.sample_spec)
        
        # Test that code can be parsed by AST
        try:
            ast.parse(result.generated_code)
            syntax_valid = True
        except SyntaxError:
            syntax_valid = False
        
        self.assertTrue(syntax_valid)
        self.assertTrue(result.analysis_result.syntax_valid)
        self.assertTrue(result.analysis_result.ast_parseable)
    
    def test_cube_geometry_generation(self):
        """Test code generation for cube geometry."""
        result = self.agent.generate_blender_code(self.sample_spec)
        code = result.generated_code
        
        # Check that cube-specific code is present
        self.assertIn('primitive_cube_add', code)
        self.assertIn('size=2.0', code)
        self.assertIn('Test Cube', code)
    
    def test_sphere_geometry_generation(self):
        """Test code generation for sphere geometry."""
        sphere_spec = self.sample_spec.copy()
        sphere_spec['objects'][0]['geometry'] = {
            "type": "sphere",
            "radius": 1.5,
            "subdivisions": 4
        }
        
        result = self.agent.generate_blender_code(sphere_spec)
        code = result.generated_code
        
        # Check that sphere-specific code is present
        self.assertIn('primitive_uv_sphere_add', code)
        self.assertIn('radius=1.5', code)
        self.assertIn('subdivisions=4', code)
    
    def test_cylinder_geometry_generation(self):
        """Test code generation for cylinder geometry."""
        cylinder_spec = self.sample_spec.copy()
        cylinder_spec['objects'][0]['geometry'] = {
            "type": "cylinder",
            "radius": 1.0,
            "height": 3.0,
            "vertices": 16
        }
        
        result = self.agent.generate_blender_code(cylinder_spec)
        code = result.generated_code
        
        # Check that cylinder-specific code is present
        self.assertIn('primitive_cylinder_add', code)
        self.assertIn('radius=1.0', code)
        self.assertIn('depth=3.0', code)
        self.assertIn('vertices=16', code)
    
    def test_plane_geometry_generation(self):
        """Test code generation for plane geometry."""
        plane_spec = self.sample_spec.copy()
        plane_spec['objects'][0]['geometry'] = {
            "type": "plane",
            "size": 4.0
        }
        
        result = self.agent.generate_blender_code(plane_spec)
        code = result.generated_code
        
        # Check that plane-specific code is present
        self.assertIn('primitive_plane_add', code)
        self.assertIn('size=4.0', code)
    
    def test_cone_geometry_generation(self):
        """Test code generation for cone geometry."""
        cone_spec = self.sample_spec.copy()
        cone_spec['objects'][0]['geometry'] = {
            "type": "cone",
            "radius": 2.0,
            "height": 4.0,
            "vertices": 8
        }
        
        result = self.agent.generate_blender_code(cone_spec)
        code = result.generated_code
        
        # Check that cone-specific code is present
        self.assertIn('primitive_cone_add', code)
        self.assertIn('radius1=2.0', code)
        self.assertIn('depth=4.0', code)
        self.assertIn('vertices=8', code)
    
    def test_material_generation(self):
        """Test material code generation."""
        result = self.agent.generate_blender_code(self.sample_spec)
        code = result.generated_code
        
        # Check that material code is present
        self.assertIn('red_material', code)
        self.assertIn('materials.new', code)
        self.assertIn('(1.0, 0.0, 0.0, 1.0)', code)  # Red color
    
    def test_transform_generation(self):
        """Test transform (position, rotation, scale) code generation."""
        # Test with non-default transforms
        transform_spec = self.sample_spec.copy()
        transform_spec['objects'][0]['transform'] = {
            "position": {"x": 1.0, "y": 2.0, "z": 3.0},
            "rotation": {"x": 0.1, "y": 0.2, "z": 0.3},
            "scale": {"x": 2.0, "y": 1.5, "z": 0.5}
        }
        
        result = self.agent.generate_blender_code(transform_spec)
        code = result.generated_code
        
        # Check that transform values are present
        self.assertIn('location=(1.0, 2.0, 3.0)', code)
        self.assertIn('rotation_euler = (0.1, 0.2, 0.3)', code)
        self.assertIn('scale = (2.0, 1.5, 0.5)', code)
    
    def test_multiple_objects_generation(self):
        """Test code generation for multiple objects."""
        multi_spec = self.sample_spec.copy()
        multi_spec['objects'].append({
            "id": "sphere_1",
            "name": "Test Sphere",
            "geometry": {
                "type": "sphere",
                "radius": 1.0
            },
            "transform": {
                "position": {"x": 3.0, "y": 0.0, "z": 0.0}
            }
        })
        
        result = self.agent.generate_blender_code(multi_spec)
        code = result.generated_code
        
        # Check that both objects are present
        self.assertIn('Test Cube', code)
        self.assertIn('Test Sphere', code)
        self.assertIn('primitive_cube_add', code)
        self.assertIn('primitive_uv_sphere_add', code)
    
    def test_static_code_analysis(self):
        """Test static code analysis functionality."""
        result = self.agent.generate_blender_code(self.sample_spec)
        analysis = result.analysis_result
        
        self.assertIsInstance(analysis, CodeAnalysisResult)
        self.assertTrue(analysis.syntax_valid)
        self.assertTrue(analysis.ast_parseable)
        self.assertGreaterEqual(analysis.quality_score, 0.0)
        self.assertLessEqual(analysis.quality_score, 1.0)
        self.assertIsInstance(analysis.quality_level, CodeQuality)
        self.assertGreater(analysis.line_count, 0)
    
    def test_code_validation_only(self):
        """Test standalone code validation."""
        valid_code = "import bpy\nbpy.ops.mesh.primitive_cube_add()"
        invalid_code = "import bpy\nbpy.ops.mesh.primitive_cube_add("  # Missing closing parenthesis
        
        valid_result = self.agent.validate_code_only(valid_code)
        invalid_result = self.agent.validate_code_only(invalid_code)
        
        self.assertTrue(valid_result.syntax_valid)
        self.assertTrue(valid_result.ast_parseable)
        
        self.assertFalse(invalid_result.syntax_valid)
        self.assertFalse(invalid_result.ast_parseable)
        self.assertGreater(len(invalid_result.issues), 0)
    
    def test_supported_geometry_types(self):
        """Test getting supported geometry types."""
        types = self.agent.get_supported_geometry_types()
        expected_types = ['cube', 'sphere', 'cylinder', 'plane', 'cone']
        
        self.assertEqual(set(types), set(expected_types))
    
    def test_code_template_access(self):
        """Test accessing code templates."""
        cube_template = self.agent.get_code_template('cube')
        self.assertIn('primitive_cube_add', cube_template)
        
        with self.assertRaises(CodeGenerationError):
            self.agent.get_code_template('invalid_type')

    def test_specification_validation(self):
        """Test specification validation."""
        # Test valid specification
        self.agent._validate_specification(self.sample_spec)  # Should not raise

        # Test missing required fields
        invalid_spec = {"schema_version": "v1.0.0"}
        with self.assertRaises(CodeGenerationError):
            self.agent._validate_specification(invalid_spec)

        # Test empty objects list
        empty_objects_spec = self.sample_spec.copy()
        empty_objects_spec['objects'] = []
        with self.assertRaises(CodeGenerationError):
            self.agent._validate_specification(empty_objects_spec)

        # Test unsupported geometry type
        unsupported_spec = self.sample_spec.copy()
        unsupported_spec['objects'][0]['geometry']['type'] = 'pyramid'
        with self.assertRaises(CodeGenerationError):
            self.agent._validate_specification(unsupported_spec)

    @patch('agents.code_generation_agent.KnowledgeAgent')
    def test_knowledge_agent_integration(self, mock_knowledge_agent):
        """Test integration with Knowledge Agent."""
        # Setup mock knowledge agent
        mock_agent = Mock()
        mock_agent.query_knowledge.return_value = [self.sample_knowledge_result]

        # Create agent with knowledge agent
        agent = CodeGenerationAgent(
            config=self.config,
            knowledge_agent=mock_agent
        )

        result = agent.generate_blender_code(self.sample_spec)

        # Verify knowledge agent was called
        mock_agent.query_knowledge.assert_called_once()

        # Verify knowledge context was used
        self.assertGreater(len(result.knowledge_context_used), 0)
        self.assertIn("bpy.ops.mesh.primitive_cube_add", result.knowledge_context_used[0])

    def test_error_handling(self):
        """Test error handling scenarios."""
        # Test with malformed specification
        malformed_spec = {"invalid": "spec"}

        with self.assertRaises(CodeGenerationError):
            self.agent.generate_blender_code(malformed_spec)

    def test_code_saving(self):
        """Test code saving functionality."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "test_output.py")

            result = self.agent.generate_blender_code(
                self.sample_spec,
                output_path=output_path
            )

            # Verify file was created
            self.assertTrue(os.path.exists(output_path))

            # Verify file content
            with open(output_path, 'r') as f:
                saved_code = f.read()

            self.assertEqual(saved_code, result.generated_code)

    def test_confidence_scoring(self):
        """Test confidence score calculation."""
        result = self.agent.generate_blender_code(self.sample_spec)

        # Confidence should be reasonable for valid code
        self.assertGreaterEqual(result.confidence_score, 0.5)
        self.assertLessEqual(result.confidence_score, 1.0)

        # Test with knowledge context
        mock_knowledge_agent = Mock()
        mock_knowledge_agent.query_knowledge.return_value = [self.sample_knowledge_result]

        agent_with_knowledge = CodeGenerationAgent(
            config=self.config,
            knowledge_agent=mock_knowledge_agent
        )

        result_with_knowledge = agent_with_knowledge.generate_blender_code(self.sample_spec)

        # Confidence should be higher with knowledge context
        self.assertGreaterEqual(result_with_knowledge.confidence_score, result.confidence_score)

    def test_quality_assessment(self):
        """Test code quality assessment."""
        result = self.agent.generate_blender_code(self.sample_spec)
        analysis = result.analysis_result

        # Quality should be good for generated code
        self.assertIn(analysis.quality_level, [CodeQuality.GOOD, CodeQuality.EXCELLENT, CodeQuality.ACCEPTABLE])

        # Test quality assessment of poor code
        poor_code = "import bpy\n# This is a comment\n# Another comment\n"
        poor_analysis = self.agent.validate_code_only(poor_code)

        # Just verify that poor code has a reasonable quality score
        self.assertGreaterEqual(poor_analysis.quality_score, 0.0)
        self.assertLessEqual(poor_analysis.quality_score, 1.0)

    def test_complexity_calculation(self):
        """Test code complexity calculation."""
        # Simple code should have low complexity
        simple_code = "import bpy\nbpy.ops.mesh.primitive_cube_add()"
        simple_analysis = self.agent.validate_code_only(simple_code)

        # Complex code should have higher complexity
        complex_code = """
import bpy

def create_objects():
    for i in range(10):
        if i % 2 == 0:
            bpy.ops.mesh.primitive_cube_add()
        else:
            try:
                bpy.ops.mesh.primitive_sphere_add()
            except:
                pass

class ModelGenerator:
    def __init__(self):
        pass

    def generate(self):
        create_objects()
"""
        complex_analysis = self.agent.validate_code_only(complex_code)

        self.assertGreater(complex_analysis.complexity_score, simple_analysis.complexity_score)

    @patch('agents.code_generation_agent.OpenAI')
    def test_llm_code_generation(self, mock_openai):
        """Test LLM-based code generation."""
        # Setup mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = """```python
import bpy
bpy.ops.mesh.primitive_cube_add(size=2.0)
```"""

        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client

        # Create agent with OpenAI client
        agent = CodeGenerationAgent(
            config=self.config,
            openai_api_key="test_key"
        )
        agent.openai_client = mock_client

        # Test LLM generation
        code = agent.generate_code_with_llm(self.sample_spec, [])

        self.assertIn("import bpy", code)
        self.assertIn("primitive_cube_add", code)
        mock_client.chat.completions.create.assert_called_once()

    def test_default_values_handling(self):
        """Test handling of default values in specifications."""
        # Test specification with minimal object (using defaults)
        minimal_spec = {
            "schema_version": "v1.0.0",
            "model_info": {
                "name": "Minimal Model",
                "description": "A minimal test model"
            },
            "objects": [
                {
                    "id": "cube_minimal",
                    "name": "Minimal Cube",
                    "geometry": {
                        "type": "cube"
                        # No size specified - should use default
                    }
                    # No transform or material specified - should use defaults
                }
            ]
        }

        result = self.agent.generate_blender_code(minimal_spec)
        code = result.generated_code

        # Should still generate valid code with defaults
        self.assertTrue(result.analysis_result.syntax_valid)
        self.assertIn("primitive_cube_add", code)
        self.assertIn("Minimal Cube", code)


if __name__ == '__main__':
    unittest.main()
